/*! For license information please see main.c567c08b.js.LICENSE.txt */
(()=>{"use strict";var e={730:(e,t,n)=>{var r=n(43),a=n(853);function o(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var l=new Set,i={};function u(e,t){s(e,t),s(e+"Capture",t)}function s(e,t){for(i[e]=t,e=0;e<t.length;e++)l.add(t[e])}var c=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,p={},h={};function m(e,t,n,r,a,o,l){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=l}var g={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){g[e]=new m(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];g[t]=new m(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){g[e]=new m(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){g[e]=new m(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){g[e]=new m(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){g[e]=new m(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){g[e]=new m(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){g[e]=new m(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){g[e]=new m(e,5,!1,e.toLowerCase(),null,!1,!1)}));var y=/[\-:]([a-z])/g;function v(e){return e[1].toUpperCase()}function b(e,t,n,r){var a=g.hasOwnProperty(t)?g[t]:null;(null!==a?0!==a.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,a,r)&&(n=null),r||null===a?function(e){return!!d.call(h,e)||!d.call(p,e)&&(f.test(e)?h[e]=!0:(p[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=null===n?3!==a.type&&"":n:(t=a.attributeName,r=a.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(a=a.type)||4===a&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(y,v);g[t]=new m(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(y,v);g[t]=new m(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(y,v);g[t]=new m(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!1,!1)})),g.xlinkHref=new m("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!0,!0)}));var w=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,k=Symbol.for("react.element"),S=Symbol.for("react.portal"),E=Symbol.for("react.fragment"),x=Symbol.for("react.strict_mode"),C=Symbol.for("react.profiler"),N=Symbol.for("react.provider"),R=Symbol.for("react.context"),_=Symbol.for("react.forward_ref"),T=Symbol.for("react.suspense"),P=Symbol.for("react.suspense_list"),O=Symbol.for("react.memo"),L=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var j=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var z=Symbol.iterator;function F(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=z&&e[z]||e["@@iterator"])?e:null}var A,M=Object.assign;function D(e){if(void 0===A)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);A=t&&t[1]||""}return"\n"+A+e}var U=!1;function I(e,t){if(!e||U)return"";U=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(s){var r=s}Reflect.construct(e,[],t)}else{try{t.call()}catch(s){r=s}e.call(t.prototype)}else{try{throw Error()}catch(s){r=s}e()}}catch(s){if(s&&r&&"string"===typeof s.stack){for(var a=s.stack.split("\n"),o=r.stack.split("\n"),l=a.length-1,i=o.length-1;1<=l&&0<=i&&a[l]!==o[i];)i--;for(;1<=l&&0<=i;l--,i--)if(a[l]!==o[i]){if(1!==l||1!==i)do{if(l--,0>--i||a[l]!==o[i]){var u="\n"+a[l].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}}while(1<=l&&0<=i);break}}}finally{U=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?D(e):""}function $(e){switch(e.tag){case 5:return D(e.type);case 16:return D("Lazy");case 13:return D("Suspense");case 19:return D("SuspenseList");case 0:case 2:case 15:return e=I(e.type,!1);case 11:return e=I(e.type.render,!1);case 1:return e=I(e.type,!0);default:return""}}function B(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case E:return"Fragment";case S:return"Portal";case C:return"Profiler";case x:return"StrictMode";case T:return"Suspense";case P:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case R:return(e.displayName||"Context")+".Consumer";case N:return(e._context.displayName||"Context")+".Provider";case _:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case O:return null!==(t=e.displayName||null)?t:B(e.type)||"Memo";case L:t=e._payload,e=e._init;try{return B(e(t))}catch(n){}}return null}function W(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return B(t);case 8:return t===x?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function H(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function V(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function q(e){e._valueTracker||(e._valueTracker=function(e){var t=V(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,o.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function Q(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=V(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function K(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function J(e,t){var n=t.checked;return M({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function Y(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=H(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function X(e,t){null!=(t=t.checked)&&b(e,"checked",t,!1)}function G(e,t){X(e,t);var n=H(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,H(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Z(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&K(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+H(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(o(91));return M({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ae(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(o(92));if(te(n)){if(1<n.length)throw Error(o(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:H(n)}}function oe(e,t){var n=H(t.value),r=H(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function le(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function ie(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function ue(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?ie(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var se,ce,de=(ce=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((se=se||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=se.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction((function(){return ce(e,t)}))}:ce);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var pe={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},he=["Webkit","ms","Moz","O"];function me(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||pe.hasOwnProperty(e)&&pe[e]?(""+t).trim():t+"px"}function ge(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),a=me(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}Object.keys(pe).forEach((function(e){he.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),pe[t]=pe[e]}))}));var ye=M({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ve(e,t){if(t){if(ye[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(o(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(o(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(o(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(o(62))}}function be(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var we=null;function ke(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Se=null,Ee=null,xe=null;function Ce(e){if(e=ba(e)){if("function"!==typeof Se)throw Error(o(280));var t=e.stateNode;t&&(t=ka(t),Se(e.stateNode,e.type,t))}}function Ne(e){Ee?xe?xe.push(e):xe=[e]:Ee=e}function Re(){if(Ee){var e=Ee,t=xe;if(xe=Ee=null,Ce(e),t)for(e=0;e<t.length;e++)Ce(t[e])}}function _e(e,t){return e(t)}function Te(){}var Pe=!1;function Oe(e,t,n){if(Pe)return e(t,n);Pe=!0;try{return _e(e,t,n)}finally{Pe=!1,(null!==Ee||null!==xe)&&(Te(),Re())}}function Le(e,t){var n=e.stateNode;if(null===n)return null;var r=ka(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(o(231,t,typeof n));return n}var je=!1;if(c)try{var ze={};Object.defineProperty(ze,"passive",{get:function(){je=!0}}),window.addEventListener("test",ze,ze),window.removeEventListener("test",ze,ze)}catch(ce){je=!1}function Fe(e,t,n,r,a,o,l,i,u){var s=Array.prototype.slice.call(arguments,3);try{t.apply(n,s)}catch(c){this.onError(c)}}var Ae=!1,Me=null,De=!1,Ue=null,Ie={onError:function(e){Ae=!0,Me=e}};function $e(e,t,n,r,a,o,l,i,u){Ae=!1,Me=null,Fe.apply(Ie,arguments)}function Be(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function We(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function He(e){if(Be(e)!==e)throw Error(o(188))}function Ve(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=Be(e)))throw Error(o(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var l=a.alternate;if(null===l){if(null!==(r=a.return)){n=r;continue}break}if(a.child===l.child){for(l=a.child;l;){if(l===n)return He(a),e;if(l===r)return He(a),t;l=l.sibling}throw Error(o(188))}if(n.return!==r.return)n=a,r=l;else{for(var i=!1,u=a.child;u;){if(u===n){i=!0,n=a,r=l;break}if(u===r){i=!0,r=a,n=l;break}u=u.sibling}if(!i){for(u=l.child;u;){if(u===n){i=!0,n=l,r=a;break}if(u===r){i=!0,r=l,n=a;break}u=u.sibling}if(!i)throw Error(o(189))}}if(n.alternate!==r)throw Error(o(190))}if(3!==n.tag)throw Error(o(188));return n.stateNode.current===n?e:t}(e))?qe(e):null}function qe(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=qe(e);if(null!==t)return t;e=e.sibling}return null}var Qe=a.unstable_scheduleCallback,Ke=a.unstable_cancelCallback,Je=a.unstable_shouldYield,Ye=a.unstable_requestPaint,Xe=a.unstable_now,Ge=a.unstable_getCurrentPriorityLevel,Ze=a.unstable_ImmediatePriority,et=a.unstable_UserBlockingPriority,tt=a.unstable_NormalPriority,nt=a.unstable_LowPriority,rt=a.unstable_IdlePriority,at=null,ot=null;var lt=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(it(e)/ut|0)|0},it=Math.log,ut=Math.LN2;var st=64,ct=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,a=e.suspendedLanes,o=e.pingedLanes,l=268435455&n;if(0!==l){var i=l&~a;0!==i?r=dt(i):0!==(o&=l)&&(r=dt(o))}else 0!==(l=n&~a)?r=dt(l):0!==o&&(r=dt(o));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&a)&&((a=r&-r)>=(o=t&-t)||16===a&&0!==(4194240&o)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)a=1<<(n=31-lt(t)),r|=e[n],t&=~a;return r}function pt(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function ht(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function mt(){var e=st;return 0===(4194240&(st<<=1))&&(st=64),e}function gt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function yt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-lt(t)]=n}function vt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-lt(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var bt=0;function wt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var kt,St,Et,xt,Ct,Nt=!1,Rt=[],_t=null,Tt=null,Pt=null,Ot=new Map,Lt=new Map,jt=[],zt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Ft(e,t){switch(e){case"focusin":case"focusout":_t=null;break;case"dragenter":case"dragleave":Tt=null;break;case"mouseover":case"mouseout":Pt=null;break;case"pointerover":case"pointerout":Ot.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Lt.delete(t.pointerId)}}function At(e,t,n,r,a,o){return null===e||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[a]},null!==t&&(null!==(t=ba(t))&&St(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function Mt(e){var t=va(e.target);if(null!==t){var n=Be(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=We(n)))return e.blockedOn=t,void Ct(e.priority,(function(){Et(n)}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Dt(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Jt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=ba(n))&&St(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);we=r,n.target.dispatchEvent(r),we=null,t.shift()}return!0}function Ut(e,t,n){Dt(e)&&n.delete(t)}function It(){Nt=!1,null!==_t&&Dt(_t)&&(_t=null),null!==Tt&&Dt(Tt)&&(Tt=null),null!==Pt&&Dt(Pt)&&(Pt=null),Ot.forEach(Ut),Lt.forEach(Ut)}function $t(e,t){e.blockedOn===t&&(e.blockedOn=null,Nt||(Nt=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,It)))}function Bt(e){function t(t){return $t(t,e)}if(0<Rt.length){$t(Rt[0],e);for(var n=1;n<Rt.length;n++){var r=Rt[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==_t&&$t(_t,e),null!==Tt&&$t(Tt,e),null!==Pt&&$t(Pt,e),Ot.forEach(t),Lt.forEach(t),n=0;n<jt.length;n++)(r=jt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<jt.length&&null===(n=jt[0]).blockedOn;)Mt(n),null===n.blockedOn&&jt.shift()}var Wt=w.ReactCurrentBatchConfig,Ht=!0;function Vt(e,t,n,r){var a=bt,o=Wt.transition;Wt.transition=null;try{bt=1,Qt(e,t,n,r)}finally{bt=a,Wt.transition=o}}function qt(e,t,n,r){var a=bt,o=Wt.transition;Wt.transition=null;try{bt=4,Qt(e,t,n,r)}finally{bt=a,Wt.transition=o}}function Qt(e,t,n,r){if(Ht){var a=Jt(e,t,n,r);if(null===a)Hr(e,t,r,Kt,n),Ft(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return _t=At(_t,e,t,n,r,a),!0;case"dragenter":return Tt=At(Tt,e,t,n,r,a),!0;case"mouseover":return Pt=At(Pt,e,t,n,r,a),!0;case"pointerover":var o=a.pointerId;return Ot.set(o,At(Ot.get(o)||null,e,t,n,r,a)),!0;case"gotpointercapture":return o=a.pointerId,Lt.set(o,At(Lt.get(o)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(Ft(e,r),4&t&&-1<zt.indexOf(e)){for(;null!==a;){var o=ba(a);if(null!==o&&kt(o),null===(o=Jt(e,t,n,r))&&Hr(e,t,r,Kt,n),o===a)break;a=o}null!==a&&r.stopPropagation()}else Hr(e,t,r,null,n)}}var Kt=null;function Jt(e,t,n,r){if(Kt=null,null!==(e=va(e=ke(r))))if(null===(t=Be(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=We(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Kt=e,null}function Yt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ge()){case Ze:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Xt=null,Gt=null,Zt=null;function en(){if(Zt)return Zt;var e,t,n=Gt,r=n.length,a="value"in Xt?Xt.value:Xt.textContent,o=a.length;for(e=0;e<r&&n[e]===a[e];e++);var l=r-e;for(t=1;t<=l&&n[r-t]===a[o-t];t++);return Zt=a.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function an(e){function t(t,n,r,a,o){for(var l in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=o,this.currentTarget=null,e)e.hasOwnProperty(l)&&(t=e[l],this[l]=t?t(a):a[l]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return M(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var on,ln,un,sn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},cn=an(sn),dn=M({},sn,{view:0,detail:0}),fn=an(dn),pn=M({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Cn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==un&&(un&&"mousemove"===e.type?(on=e.screenX-un.screenX,ln=e.screenY-un.screenY):ln=on=0,un=e),on)},movementY:function(e){return"movementY"in e?e.movementY:ln}}),hn=an(pn),mn=an(M({},pn,{dataTransfer:0})),gn=an(M({},dn,{relatedTarget:0})),yn=an(M({},sn,{animationName:0,elapsedTime:0,pseudoElement:0})),vn=M({},sn,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),bn=an(vn),wn=an(M({},sn,{data:0})),kn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Sn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},En={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function xn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=En[e])&&!!t[e]}function Cn(){return xn}var Nn=M({},dn,{key:function(e){if(e.key){var t=kn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?Sn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Cn,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),Rn=an(Nn),_n=an(M({},pn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Tn=an(M({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Cn})),Pn=an(M({},sn,{propertyName:0,elapsedTime:0,pseudoElement:0})),On=M({},pn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Ln=an(On),jn=[9,13,27,32],zn=c&&"CompositionEvent"in window,Fn=null;c&&"documentMode"in document&&(Fn=document.documentMode);var An=c&&"TextEvent"in window&&!Fn,Mn=c&&(!zn||Fn&&8<Fn&&11>=Fn),Dn=String.fromCharCode(32),Un=!1;function In(e,t){switch(e){case"keyup":return-1!==jn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function $n(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Bn=!1;var Wn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Hn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Wn[e.type]:"textarea"===t}function Vn(e,t,n,r){Ne(r),0<(t=qr(t,"onChange")).length&&(n=new cn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var qn=null,Qn=null;function Kn(e){Dr(e,0)}function Jn(e){if(Q(wa(e)))return e}function Yn(e,t){if("change"===e)return t}var Xn=!1;if(c){var Gn;if(c){var Zn="oninput"in document;if(!Zn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Zn="function"===typeof er.oninput}Gn=Zn}else Gn=!1;Xn=Gn&&(!document.documentMode||9<document.documentMode)}function tr(){qn&&(qn.detachEvent("onpropertychange",nr),Qn=qn=null)}function nr(e){if("value"===e.propertyName&&Jn(Qn)){var t=[];Vn(t,Qn,e,ke(e)),Oe(Kn,t)}}function rr(e,t,n){"focusin"===e?(tr(),Qn=n,(qn=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function ar(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Jn(Qn)}function or(e,t){if("click"===e)return Jn(t)}function lr(e,t){if("input"===e||"change"===e)return Jn(t)}var ir="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function ur(e,t){if(ir(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!d.call(t,a)||!ir(e[a],t[a]))return!1}return!0}function sr(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function cr(e,t){var n,r=sr(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=sr(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=K();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=K((e=t.contentWindow).document)}return t}function pr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function hr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&pr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var a=n.textContent.length,o=Math.min(r.start,a);r=void 0===r.end?o:Math.min(r.end,a),!e.extend&&o>r&&(a=r,r=o,o=a),a=cr(n,o);var l=cr(n,r);a&&l&&(1!==e.rangeCount||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==l.node||e.focusOffset!==l.offset)&&((t=t.createRange()).setStart(a.node,a.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(l.node,l.offset)):(t.setEnd(l.node,l.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var mr=c&&"documentMode"in document&&11>=document.documentMode,gr=null,yr=null,vr=null,br=!1;function wr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;br||null==gr||gr!==K(r)||("selectionStart"in(r=gr)&&pr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},vr&&ur(vr,r)||(vr=r,0<(r=qr(yr,"onSelect")).length&&(t=new cn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=gr)))}function kr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Sr={animationend:kr("Animation","AnimationEnd"),animationiteration:kr("Animation","AnimationIteration"),animationstart:kr("Animation","AnimationStart"),transitionend:kr("Transition","TransitionEnd")},Er={},xr={};function Cr(e){if(Er[e])return Er[e];if(!Sr[e])return e;var t,n=Sr[e];for(t in n)if(n.hasOwnProperty(t)&&t in xr)return Er[e]=n[t];return e}c&&(xr=document.createElement("div").style,"AnimationEvent"in window||(delete Sr.animationend.animation,delete Sr.animationiteration.animation,delete Sr.animationstart.animation),"TransitionEvent"in window||delete Sr.transitionend.transition);var Nr=Cr("animationend"),Rr=Cr("animationiteration"),_r=Cr("animationstart"),Tr=Cr("transitionend"),Pr=new Map,Or="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Lr(e,t){Pr.set(e,t),u(t,[e])}for(var jr=0;jr<Or.length;jr++){var zr=Or[jr];Lr(zr.toLowerCase(),"on"+(zr[0].toUpperCase()+zr.slice(1)))}Lr(Nr,"onAnimationEnd"),Lr(Rr,"onAnimationIteration"),Lr(_r,"onAnimationStart"),Lr("dblclick","onDoubleClick"),Lr("focusin","onFocus"),Lr("focusout","onBlur"),Lr(Tr,"onTransitionEnd"),s("onMouseEnter",["mouseout","mouseover"]),s("onMouseLeave",["mouseout","mouseover"]),s("onPointerEnter",["pointerout","pointerover"]),s("onPointerLeave",["pointerout","pointerover"]),u("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),u("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),u("onBeforeInput",["compositionend","keypress","textInput","paste"]),u("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),u("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),u("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Fr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ar=new Set("cancel close invalid load scroll toggle".split(" ").concat(Fr));function Mr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,a,l,i,u,s){if($e.apply(this,arguments),Ae){if(!Ae)throw Error(o(198));var c=Me;Ae=!1,Me=null,De||(De=!0,Ue=c)}}(r,t,void 0,e),e.currentTarget=null}function Dr(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var l=r.length-1;0<=l;l--){var i=r[l],u=i.instance,s=i.currentTarget;if(i=i.listener,u!==o&&a.isPropagationStopped())break e;Mr(a,i,s),o=u}else for(l=0;l<r.length;l++){if(u=(i=r[l]).instance,s=i.currentTarget,i=i.listener,u!==o&&a.isPropagationStopped())break e;Mr(a,i,s),o=u}}}if(De)throw e=Ue,De=!1,Ue=null,e}function Ur(e,t){var n=t[ma];void 0===n&&(n=t[ma]=new Set);var r=e+"__bubble";n.has(r)||(Wr(t,e,2,!1),n.add(r))}function Ir(e,t,n){var r=0;t&&(r|=4),Wr(n,e,r,t)}var $r="_reactListening"+Math.random().toString(36).slice(2);function Br(e){if(!e[$r]){e[$r]=!0,l.forEach((function(t){"selectionchange"!==t&&(Ar.has(t)||Ir(t,!1,e),Ir(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[$r]||(t[$r]=!0,Ir("selectionchange",!1,t))}}function Wr(e,t,n,r){switch(Yt(t)){case 1:var a=Vt;break;case 4:a=qt;break;default:a=Qt}n=a.bind(null,t,n,e),a=void 0,!je||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Hr(e,t,n,r,a){var o=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var l=r.tag;if(3===l||4===l){var i=r.stateNode.containerInfo;if(i===a||8===i.nodeType&&i.parentNode===a)break;if(4===l)for(l=r.return;null!==l;){var u=l.tag;if((3===u||4===u)&&((u=l.stateNode.containerInfo)===a||8===u.nodeType&&u.parentNode===a))return;l=l.return}for(;null!==i;){if(null===(l=va(i)))return;if(5===(u=l.tag)||6===u){r=o=l;continue e}i=i.parentNode}}r=r.return}Oe((function(){var r=o,a=ke(n),l=[];e:{var i=Pr.get(e);if(void 0!==i){var u=cn,s=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":u=Rn;break;case"focusin":s="focus",u=gn;break;case"focusout":s="blur",u=gn;break;case"beforeblur":case"afterblur":u=gn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":u=hn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":u=mn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":u=Tn;break;case Nr:case Rr:case _r:u=yn;break;case Tr:u=Pn;break;case"scroll":u=fn;break;case"wheel":u=Ln;break;case"copy":case"cut":case"paste":u=bn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":u=_n}var c=0!==(4&t),d=!c&&"scroll"===e,f=c?null!==i?i+"Capture":null:i;c=[];for(var p,h=r;null!==h;){var m=(p=h).stateNode;if(5===p.tag&&null!==m&&(p=m,null!==f&&(null!=(m=Le(h,f))&&c.push(Vr(h,m,p)))),d)break;h=h.return}0<c.length&&(i=new u(i,s,null,n,a),l.push({event:i,listeners:c}))}}if(0===(7&t)){if(u="mouseout"===e||"pointerout"===e,(!(i="mouseover"===e||"pointerover"===e)||n===we||!(s=n.relatedTarget||n.fromElement)||!va(s)&&!s[ha])&&(u||i)&&(i=a.window===a?a:(i=a.ownerDocument)?i.defaultView||i.parentWindow:window,u?(u=r,null!==(s=(s=n.relatedTarget||n.toElement)?va(s):null)&&(s!==(d=Be(s))||5!==s.tag&&6!==s.tag)&&(s=null)):(u=null,s=r),u!==s)){if(c=hn,m="onMouseLeave",f="onMouseEnter",h="mouse","pointerout"!==e&&"pointerover"!==e||(c=_n,m="onPointerLeave",f="onPointerEnter",h="pointer"),d=null==u?i:wa(u),p=null==s?i:wa(s),(i=new c(m,h+"leave",u,n,a)).target=d,i.relatedTarget=p,m=null,va(a)===r&&((c=new c(f,h+"enter",s,n,a)).target=p,c.relatedTarget=d,m=c),d=m,u&&s)e:{for(f=s,h=0,p=c=u;p;p=Qr(p))h++;for(p=0,m=f;m;m=Qr(m))p++;for(;0<h-p;)c=Qr(c),h--;for(;0<p-h;)f=Qr(f),p--;for(;h--;){if(c===f||null!==f&&c===f.alternate)break e;c=Qr(c),f=Qr(f)}c=null}else c=null;null!==u&&Kr(l,i,u,c,!1),null!==s&&null!==d&&Kr(l,d,s,c,!0)}if("select"===(u=(i=r?wa(r):window).nodeName&&i.nodeName.toLowerCase())||"input"===u&&"file"===i.type)var g=Yn;else if(Hn(i))if(Xn)g=lr;else{g=ar;var y=rr}else(u=i.nodeName)&&"input"===u.toLowerCase()&&("checkbox"===i.type||"radio"===i.type)&&(g=or);switch(g&&(g=g(e,r))?Vn(l,g,n,a):(y&&y(e,i,r),"focusout"===e&&(y=i._wrapperState)&&y.controlled&&"number"===i.type&&ee(i,"number",i.value)),y=r?wa(r):window,e){case"focusin":(Hn(y)||"true"===y.contentEditable)&&(gr=y,yr=r,vr=null);break;case"focusout":vr=yr=gr=null;break;case"mousedown":br=!0;break;case"contextmenu":case"mouseup":case"dragend":br=!1,wr(l,n,a);break;case"selectionchange":if(mr)break;case"keydown":case"keyup":wr(l,n,a)}var v;if(zn)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Bn?In(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(Mn&&"ko"!==n.locale&&(Bn||"onCompositionStart"!==b?"onCompositionEnd"===b&&Bn&&(v=en()):(Gt="value"in(Xt=a)?Xt.value:Xt.textContent,Bn=!0)),0<(y=qr(r,b)).length&&(b=new wn(b,e,null,n,a),l.push({event:b,listeners:y}),v?b.data=v:null!==(v=$n(n))&&(b.data=v))),(v=An?function(e,t){switch(e){case"compositionend":return $n(t);case"keypress":return 32!==t.which?null:(Un=!0,Dn);case"textInput":return(e=t.data)===Dn&&Un?null:e;default:return null}}(e,n):function(e,t){if(Bn)return"compositionend"===e||!zn&&In(e,t)?(e=en(),Zt=Gt=Xt=null,Bn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Mn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=qr(r,"onBeforeInput")).length&&(a=new wn("onBeforeInput","beforeinput",null,n,a),l.push({event:a,listeners:r}),a.data=v))}Dr(l,t)}))}function Vr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function qr(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,o=a.stateNode;5===a.tag&&null!==o&&(a=o,null!=(o=Le(e,n))&&r.unshift(Vr(e,o,a)),null!=(o=Le(e,t))&&r.push(Vr(e,o,a))),e=e.return}return r}function Qr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Kr(e,t,n,r,a){for(var o=t._reactName,l=[];null!==n&&n!==r;){var i=n,u=i.alternate,s=i.stateNode;if(null!==u&&u===r)break;5===i.tag&&null!==s&&(i=s,a?null!=(u=Le(n,o))&&l.unshift(Vr(n,u,i)):a||null!=(u=Le(n,o))&&l.push(Vr(n,u,i))),n=n.return}0!==l.length&&e.push({event:t,listeners:l})}var Jr=/\r\n?/g,Yr=/\u0000|\uFFFD/g;function Xr(e){return("string"===typeof e?e:""+e).replace(Jr,"\n").replace(Yr,"")}function Gr(e,t,n){if(t=Xr(t),Xr(e)!==t&&n)throw Error(o(425))}function Zr(){}var ea=null,ta=null;function na(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ra="function"===typeof setTimeout?setTimeout:void 0,aa="function"===typeof clearTimeout?clearTimeout:void 0,oa="function"===typeof Promise?Promise:void 0,la="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof oa?function(e){return oa.resolve(null).then(e).catch(ia)}:ra;function ia(e){setTimeout((function(){throw e}))}function ua(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0===r)return e.removeChild(a),void Bt(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=a}while(n);Bt(t)}function sa(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function ca(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var da=Math.random().toString(36).slice(2),fa="__reactFiber$"+da,pa="__reactProps$"+da,ha="__reactContainer$"+da,ma="__reactEvents$"+da,ga="__reactListeners$"+da,ya="__reactHandles$"+da;function va(e){var t=e[fa];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ha]||n[fa]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=ca(e);null!==e;){if(n=e[fa])return n;e=ca(e)}return t}n=(e=n).parentNode}return null}function ba(e){return!(e=e[fa]||e[ha])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function wa(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(o(33))}function ka(e){return e[pa]||null}var Sa=[],Ea=-1;function xa(e){return{current:e}}function Ca(e){0>Ea||(e.current=Sa[Ea],Sa[Ea]=null,Ea--)}function Na(e,t){Ea++,Sa[Ea]=e.current,e.current=t}var Ra={},_a=xa(Ra),Ta=xa(!1),Pa=Ra;function Oa(e,t){var n=e.type.contextTypes;if(!n)return Ra;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a,o={};for(a in n)o[a]=t[a];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function La(e){return null!==(e=e.childContextTypes)&&void 0!==e}function ja(){Ca(Ta),Ca(_a)}function za(e,t,n){if(_a.current!==Ra)throw Error(o(168));Na(_a,t),Na(Ta,n)}function Fa(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in t))throw Error(o(108,W(e)||"Unknown",a));return M({},n,r)}function Aa(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Ra,Pa=_a.current,Na(_a,e),Na(Ta,Ta.current),!0}function Ma(e,t,n){var r=e.stateNode;if(!r)throw Error(o(169));n?(e=Fa(e,t,Pa),r.__reactInternalMemoizedMergedChildContext=e,Ca(Ta),Ca(_a),Na(_a,e)):Ca(Ta),Na(Ta,n)}var Da=null,Ua=!1,Ia=!1;function $a(e){null===Da?Da=[e]:Da.push(e)}function Ba(){if(!Ia&&null!==Da){Ia=!0;var e=0,t=bt;try{var n=Da;for(bt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Da=null,Ua=!1}catch(a){throw null!==Da&&(Da=Da.slice(e+1)),Qe(Ze,Ba),a}finally{bt=t,Ia=!1}}return null}var Wa=[],Ha=0,Va=null,qa=0,Qa=[],Ka=0,Ja=null,Ya=1,Xa="";function Ga(e,t){Wa[Ha++]=qa,Wa[Ha++]=Va,Va=e,qa=t}function Za(e,t,n){Qa[Ka++]=Ya,Qa[Ka++]=Xa,Qa[Ka++]=Ja,Ja=e;var r=Ya;e=Xa;var a=32-lt(r)-1;r&=~(1<<a),n+=1;var o=32-lt(t)+a;if(30<o){var l=a-a%5;o=(r&(1<<l)-1).toString(32),r>>=l,a-=l,Ya=1<<32-lt(t)+a|n<<a|r,Xa=o+e}else Ya=1<<o|n<<a|r,Xa=e}function eo(e){null!==e.return&&(Ga(e,1),Za(e,1,0))}function to(e){for(;e===Va;)Va=Wa[--Ha],Wa[Ha]=null,qa=Wa[--Ha],Wa[Ha]=null;for(;e===Ja;)Ja=Qa[--Ka],Qa[Ka]=null,Xa=Qa[--Ka],Qa[Ka]=null,Ya=Qa[--Ka],Qa[Ka]=null}var no=null,ro=null,ao=!1,oo=null;function lo(e,t){var n=Os(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function io(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,no=e,ro=sa(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,no=e,ro=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Ja?{id:Ya,overflow:Xa}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Os(18,null,null,0)).stateNode=t,n.return=e,e.child=n,no=e,ro=null,!0);default:return!1}}function uo(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function so(e){if(ao){var t=ro;if(t){var n=t;if(!io(e,t)){if(uo(e))throw Error(o(418));t=sa(n.nextSibling);var r=no;t&&io(e,t)?lo(r,n):(e.flags=-4097&e.flags|2,ao=!1,no=e)}}else{if(uo(e))throw Error(o(418));e.flags=-4097&e.flags|2,ao=!1,no=e}}}function co(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;no=e}function fo(e){if(e!==no)return!1;if(!ao)return co(e),ao=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!na(e.type,e.memoizedProps)),t&&(t=ro)){if(uo(e))throw po(),Error(o(418));for(;t;)lo(e,t),t=sa(t.nextSibling)}if(co(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){ro=sa(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}ro=null}}else ro=no?sa(e.stateNode.nextSibling):null;return!0}function po(){for(var e=ro;e;)e=sa(e.nextSibling)}function ho(){ro=no=null,ao=!1}function mo(e){null===oo?oo=[e]:oo.push(e)}var go=w.ReactCurrentBatchConfig;function yo(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(o(309));var r=n.stateNode}if(!r)throw Error(o(147,e));var a=r,l=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===l?t.ref:(t=function(e){var t=a.refs;null===e?delete t[l]:t[l]=e},t._stringRef=l,t)}if("string"!==typeof e)throw Error(o(284));if(!n._owner)throw Error(o(290,e))}return e}function vo(e,t){throw e=Object.prototype.toString.call(t),Error(o(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function bo(e){return(0,e._init)(e._payload)}function wo(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=js(e,t)).index=0,e.sibling=null,e}function l(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=2),t}function u(e,t,n,r){return null===t||6!==t.tag?((t=Ms(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function s(e,t,n,r){var o=n.type;return o===E?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===o||"object"===typeof o&&null!==o&&o.$$typeof===L&&bo(o)===t.type)?((r=a(t,n.props)).ref=yo(e,t,n),r.return=e,r):((r=zs(n.type,n.key,n.props,null,e.mode,r)).ref=yo(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Ds(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,o){return null===t||7!==t.tag?((t=Fs(n,e.mode,r,o)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Ms(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case k:return(n=zs(t.type,t.key,t.props,null,e.mode,n)).ref=yo(e,null,t),n.return=e,n;case S:return(t=Ds(t,e.mode,n)).return=e,t;case L:return f(e,(0,t._init)(t._payload),n)}if(te(t)||F(t))return(t=Fs(t,e.mode,n,null)).return=e,t;vo(e,t)}return null}function p(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==a?null:u(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case k:return n.key===a?s(e,t,n,r):null;case S:return n.key===a?c(e,t,n,r):null;case L:return p(e,t,(a=n._init)(n._payload),r)}if(te(n)||F(n))return null!==a?null:d(e,t,n,r,null);vo(e,n)}return null}function h(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r)return u(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case k:return s(t,e=e.get(null===r.key?n:r.key)||null,r,a);case S:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case L:return h(e,t,n,(0,r._init)(r._payload),a)}if(te(r)||F(r))return d(t,e=e.get(n)||null,r,a,null);vo(t,r)}return null}function m(a,o,i,u){for(var s=null,c=null,d=o,m=o=0,g=null;null!==d&&m<i.length;m++){d.index>m?(g=d,d=null):g=d.sibling;var y=p(a,d,i[m],u);if(null===y){null===d&&(d=g);break}e&&d&&null===y.alternate&&t(a,d),o=l(y,o,m),null===c?s=y:c.sibling=y,c=y,d=g}if(m===i.length)return n(a,d),ao&&Ga(a,m),s;if(null===d){for(;m<i.length;m++)null!==(d=f(a,i[m],u))&&(o=l(d,o,m),null===c?s=d:c.sibling=d,c=d);return ao&&Ga(a,m),s}for(d=r(a,d);m<i.length;m++)null!==(g=h(d,a,m,i[m],u))&&(e&&null!==g.alternate&&d.delete(null===g.key?m:g.key),o=l(g,o,m),null===c?s=g:c.sibling=g,c=g);return e&&d.forEach((function(e){return t(a,e)})),ao&&Ga(a,m),s}function g(a,i,u,s){var c=F(u);if("function"!==typeof c)throw Error(o(150));if(null==(u=c.call(u)))throw Error(o(151));for(var d=c=null,m=i,g=i=0,y=null,v=u.next();null!==m&&!v.done;g++,v=u.next()){m.index>g?(y=m,m=null):y=m.sibling;var b=p(a,m,v.value,s);if(null===b){null===m&&(m=y);break}e&&m&&null===b.alternate&&t(a,m),i=l(b,i,g),null===d?c=b:d.sibling=b,d=b,m=y}if(v.done)return n(a,m),ao&&Ga(a,g),c;if(null===m){for(;!v.done;g++,v=u.next())null!==(v=f(a,v.value,s))&&(i=l(v,i,g),null===d?c=v:d.sibling=v,d=v);return ao&&Ga(a,g),c}for(m=r(a,m);!v.done;g++,v=u.next())null!==(v=h(m,a,g,v.value,s))&&(e&&null!==v.alternate&&m.delete(null===v.key?g:v.key),i=l(v,i,g),null===d?c=v:d.sibling=v,d=v);return e&&m.forEach((function(e){return t(a,e)})),ao&&Ga(a,g),c}return function e(r,o,l,u){if("object"===typeof l&&null!==l&&l.type===E&&null===l.key&&(l=l.props.children),"object"===typeof l&&null!==l){switch(l.$$typeof){case k:e:{for(var s=l.key,c=o;null!==c;){if(c.key===s){if((s=l.type)===E){if(7===c.tag){n(r,c.sibling),(o=a(c,l.props.children)).return=r,r=o;break e}}else if(c.elementType===s||"object"===typeof s&&null!==s&&s.$$typeof===L&&bo(s)===c.type){n(r,c.sibling),(o=a(c,l.props)).ref=yo(r,c,l),o.return=r,r=o;break e}n(r,c);break}t(r,c),c=c.sibling}l.type===E?((o=Fs(l.props.children,r.mode,u,l.key)).return=r,r=o):((u=zs(l.type,l.key,l.props,null,r.mode,u)).ref=yo(r,o,l),u.return=r,r=u)}return i(r);case S:e:{for(c=l.key;null!==o;){if(o.key===c){if(4===o.tag&&o.stateNode.containerInfo===l.containerInfo&&o.stateNode.implementation===l.implementation){n(r,o.sibling),(o=a(o,l.children||[])).return=r,r=o;break e}n(r,o);break}t(r,o),o=o.sibling}(o=Ds(l,r.mode,u)).return=r,r=o}return i(r);case L:return e(r,o,(c=l._init)(l._payload),u)}if(te(l))return m(r,o,l,u);if(F(l))return g(r,o,l,u);vo(r,l)}return"string"===typeof l&&""!==l||"number"===typeof l?(l=""+l,null!==o&&6===o.tag?(n(r,o.sibling),(o=a(o,l)).return=r,r=o):(n(r,o),(o=Ms(l,r.mode,u)).return=r,r=o),i(r)):n(r,o)}}var ko=wo(!0),So=wo(!1),Eo=xa(null),xo=null,Co=null,No=null;function Ro(){No=Co=xo=null}function _o(e){var t=Eo.current;Ca(Eo),e._currentValue=t}function To(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Po(e,t){xo=e,No=Co=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(bi=!0),e.firstContext=null)}function Oo(e){var t=e._currentValue;if(No!==e)if(e={context:e,memoizedValue:t,next:null},null===Co){if(null===xo)throw Error(o(308));Co=e,xo.dependencies={lanes:0,firstContext:e}}else Co=Co.next=e;return t}var Lo=null;function jo(e){null===Lo?Lo=[e]:Lo.push(e)}function zo(e,t,n,r){var a=t.interleaved;return null===a?(n.next=n,jo(t)):(n.next=a.next,a.next=n),t.interleaved=n,Fo(e,r)}function Fo(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Ao=!1;function Mo(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Do(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Uo(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Io(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&_u)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,Fo(e,n)}return null===(a=r.interleaved)?(t.next=t,jo(r)):(t.next=a.next,a.next=t),r.interleaved=t,Fo(e,n)}function $o(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,vt(e,n)}}function Bo(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,o=null;if(null!==(n=n.firstBaseUpdate)){do{var l={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===o?a=o=l:o=o.next=l,n=n.next}while(null!==n);null===o?a=o=t:o=o.next=t}else a=o=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:o,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Wo(e,t,n,r){var a=e.updateQueue;Ao=!1;var o=a.firstBaseUpdate,l=a.lastBaseUpdate,i=a.shared.pending;if(null!==i){a.shared.pending=null;var u=i,s=u.next;u.next=null,null===l?o=s:l.next=s,l=u;var c=e.alternate;null!==c&&((i=(c=c.updateQueue).lastBaseUpdate)!==l&&(null===i?c.firstBaseUpdate=s:i.next=s,c.lastBaseUpdate=u))}if(null!==o){var d=a.baseState;for(l=0,c=s=u=null,i=o;;){var f=i.lane,p=i.eventTime;if((r&f)===f){null!==c&&(c=c.next={eventTime:p,lane:0,tag:i.tag,payload:i.payload,callback:i.callback,next:null});e:{var h=e,m=i;switch(f=t,p=n,m.tag){case 1:if("function"===typeof(h=m.payload)){d=h.call(p,d,f);break e}d=h;break e;case 3:h.flags=-65537&h.flags|128;case 0:if(null===(f="function"===typeof(h=m.payload)?h.call(p,d,f):h)||void 0===f)break e;d=M({},d,f);break e;case 2:Ao=!0}}null!==i.callback&&0!==i.lane&&(e.flags|=64,null===(f=a.effects)?a.effects=[i]:f.push(i))}else p={eventTime:p,lane:f,tag:i.tag,payload:i.payload,callback:i.callback,next:null},null===c?(s=c=p,u=d):c=c.next=p,l|=f;if(null===(i=i.next)){if(null===(i=a.shared.pending))break;i=(f=i).next,f.next=null,a.lastBaseUpdate=f,a.shared.pending=null}}if(null===c&&(u=d),a.baseState=u,a.firstBaseUpdate=s,a.lastBaseUpdate=c,null!==(t=a.shared.interleaved)){a=t;do{l|=a.lane,a=a.next}while(a!==t)}else null===o&&(a.shared.lanes=0);Au|=l,e.lanes=l,e.memoizedState=d}}function Ho(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(null!==a){if(r.callback=null,r=n,"function"!==typeof a)throw Error(o(191,a));a.call(r)}}}var Vo={},qo=xa(Vo),Qo=xa(Vo),Ko=xa(Vo);function Jo(e){if(e===Vo)throw Error(o(174));return e}function Yo(e,t){switch(Na(Ko,t),Na(Qo,e),Na(qo,Vo),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:ue(null,"");break;default:t=ue(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Ca(qo),Na(qo,t)}function Xo(){Ca(qo),Ca(Qo),Ca(Ko)}function Go(e){Jo(Ko.current);var t=Jo(qo.current),n=ue(t,e.type);t!==n&&(Na(Qo,e),Na(qo,n))}function Zo(e){Qo.current===e&&(Ca(qo),Ca(Qo))}var el=xa(0);function tl(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var nl=[];function rl(){for(var e=0;e<nl.length;e++)nl[e]._workInProgressVersionPrimary=null;nl.length=0}var al=w.ReactCurrentDispatcher,ol=w.ReactCurrentBatchConfig,ll=0,il=null,ul=null,sl=null,cl=!1,dl=!1,fl=0,pl=0;function hl(){throw Error(o(321))}function ml(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ir(e[n],t[n]))return!1;return!0}function gl(e,t,n,r,a,l){if(ll=l,il=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,al.current=null===e||null===e.memoizedState?Zl:ei,e=n(r,a),dl){l=0;do{if(dl=!1,fl=0,25<=l)throw Error(o(301));l+=1,sl=ul=null,t.updateQueue=null,al.current=ti,e=n(r,a)}while(dl)}if(al.current=Gl,t=null!==ul&&null!==ul.next,ll=0,sl=ul=il=null,cl=!1,t)throw Error(o(300));return e}function yl(){var e=0!==fl;return fl=0,e}function vl(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===sl?il.memoizedState=sl=e:sl=sl.next=e,sl}function bl(){if(null===ul){var e=il.alternate;e=null!==e?e.memoizedState:null}else e=ul.next;var t=null===sl?il.memoizedState:sl.next;if(null!==t)sl=t,ul=e;else{if(null===e)throw Error(o(310));e={memoizedState:(ul=e).memoizedState,baseState:ul.baseState,baseQueue:ul.baseQueue,queue:ul.queue,next:null},null===sl?il.memoizedState=sl=e:sl=sl.next=e}return sl}function wl(e,t){return"function"===typeof t?t(e):t}function kl(e){var t=bl(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=ul,a=r.baseQueue,l=n.pending;if(null!==l){if(null!==a){var i=a.next;a.next=l.next,l.next=i}r.baseQueue=a=l,n.pending=null}if(null!==a){l=a.next,r=r.baseState;var u=i=null,s=null,c=l;do{var d=c.lane;if((ll&d)===d)null!==s&&(s=s.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===s?(u=s=f,i=r):s=s.next=f,il.lanes|=d,Au|=d}c=c.next}while(null!==c&&c!==l);null===s?i=r:s.next=u,ir(r,t.memoizedState)||(bi=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=s,n.lastRenderedState=r}if(null!==(e=n.interleaved)){a=e;do{l=a.lane,il.lanes|=l,Au|=l,a=a.next}while(a!==e)}else null===a&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Sl(e){var t=bl(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,l=t.memoizedState;if(null!==a){n.pending=null;var i=a=a.next;do{l=e(l,i.action),i=i.next}while(i!==a);ir(l,t.memoizedState)||(bi=!0),t.memoizedState=l,null===t.baseQueue&&(t.baseState=l),n.lastRenderedState=l}return[l,r]}function El(){}function xl(e,t){var n=il,r=bl(),a=t(),l=!ir(r.memoizedState,a);if(l&&(r.memoizedState=a,bi=!0),r=r.queue,Al(Rl.bind(null,n,r,e),[e]),r.getSnapshot!==t||l||null!==sl&&1&sl.memoizedState.tag){if(n.flags|=2048,Ol(9,Nl.bind(null,n,r,a,t),void 0,null),null===Tu)throw Error(o(349));0!==(30&ll)||Cl(n,t,a)}return a}function Cl(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=il.updateQueue)?(t={lastEffect:null,stores:null},il.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Nl(e,t,n,r){t.value=n,t.getSnapshot=r,_l(t)&&Tl(e)}function Rl(e,t,n){return n((function(){_l(t)&&Tl(e)}))}function _l(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ir(e,n)}catch(r){return!0}}function Tl(e){var t=Fo(e,1);null!==t&&ns(t,e,1,-1)}function Pl(e){var t=vl();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:wl,lastRenderedState:e},t.queue=e,e=e.dispatch=Kl.bind(null,il,e),[t.memoizedState,e]}function Ol(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=il.updateQueue)?(t={lastEffect:null,stores:null},il.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Ll(){return bl().memoizedState}function jl(e,t,n,r){var a=vl();il.flags|=e,a.memoizedState=Ol(1|t,n,void 0,void 0===r?null:r)}function zl(e,t,n,r){var a=bl();r=void 0===r?null:r;var o=void 0;if(null!==ul){var l=ul.memoizedState;if(o=l.destroy,null!==r&&ml(r,l.deps))return void(a.memoizedState=Ol(t,n,o,r))}il.flags|=e,a.memoizedState=Ol(1|t,n,o,r)}function Fl(e,t){return jl(8390656,8,e,t)}function Al(e,t){return zl(2048,8,e,t)}function Ml(e,t){return zl(4,2,e,t)}function Dl(e,t){return zl(4,4,e,t)}function Ul(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Il(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,zl(4,4,Ul.bind(null,t,e),n)}function $l(){}function Bl(e,t){var n=bl();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ml(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Wl(e,t){var n=bl();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&ml(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Hl(e,t,n){return 0===(21&ll)?(e.baseState&&(e.baseState=!1,bi=!0),e.memoizedState=n):(ir(n,t)||(n=mt(),il.lanes|=n,Au|=n,e.baseState=!0),t)}function Vl(e,t){var n=bt;bt=0!==n&&4>n?n:4,e(!0);var r=ol.transition;ol.transition={};try{e(!1),t()}finally{bt=n,ol.transition=r}}function ql(){return bl().memoizedState}function Ql(e,t,n){var r=ts(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Jl(e))Yl(t,n);else if(null!==(n=zo(e,t,n,r))){ns(n,e,r,es()),Xl(n,t,r)}}function Kl(e,t,n){var r=ts(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Jl(e))Yl(t,a);else{var o=e.alternate;if(0===e.lanes&&(null===o||0===o.lanes)&&null!==(o=t.lastRenderedReducer))try{var l=t.lastRenderedState,i=o(l,n);if(a.hasEagerState=!0,a.eagerState=i,ir(i,l)){var u=t.interleaved;return null===u?(a.next=a,jo(t)):(a.next=u.next,u.next=a),void(t.interleaved=a)}}catch(s){}null!==(n=zo(e,t,a,r))&&(ns(n,e,r,a=es()),Xl(n,t,r))}}function Jl(e){var t=e.alternate;return e===il||null!==t&&t===il}function Yl(e,t){dl=cl=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Xl(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,vt(e,n)}}var Gl={readContext:Oo,useCallback:hl,useContext:hl,useEffect:hl,useImperativeHandle:hl,useInsertionEffect:hl,useLayoutEffect:hl,useMemo:hl,useReducer:hl,useRef:hl,useState:hl,useDebugValue:hl,useDeferredValue:hl,useTransition:hl,useMutableSource:hl,useSyncExternalStore:hl,useId:hl,unstable_isNewReconciler:!1},Zl={readContext:Oo,useCallback:function(e,t){return vl().memoizedState=[e,void 0===t?null:t],e},useContext:Oo,useEffect:Fl,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,jl(4194308,4,Ul.bind(null,t,e),n)},useLayoutEffect:function(e,t){return jl(4194308,4,e,t)},useInsertionEffect:function(e,t){return jl(4,2,e,t)},useMemo:function(e,t){var n=vl();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=vl();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Ql.bind(null,il,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},vl().memoizedState=e},useState:Pl,useDebugValue:$l,useDeferredValue:function(e){return vl().memoizedState=e},useTransition:function(){var e=Pl(!1),t=e[0];return e=Vl.bind(null,e[1]),vl().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=il,a=vl();if(ao){if(void 0===n)throw Error(o(407));n=n()}else{if(n=t(),null===Tu)throw Error(o(349));0!==(30&ll)||Cl(r,t,n)}a.memoizedState=n;var l={value:n,getSnapshot:t};return a.queue=l,Fl(Rl.bind(null,r,l,e),[e]),r.flags|=2048,Ol(9,Nl.bind(null,r,l,n,t),void 0,null),n},useId:function(){var e=vl(),t=Tu.identifierPrefix;if(ao){var n=Xa;t=":"+t+"R"+(n=(Ya&~(1<<32-lt(Ya)-1)).toString(32)+n),0<(n=fl++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=pl++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},ei={readContext:Oo,useCallback:Bl,useContext:Oo,useEffect:Al,useImperativeHandle:Il,useInsertionEffect:Ml,useLayoutEffect:Dl,useMemo:Wl,useReducer:kl,useRef:Ll,useState:function(){return kl(wl)},useDebugValue:$l,useDeferredValue:function(e){return Hl(bl(),ul.memoizedState,e)},useTransition:function(){return[kl(wl)[0],bl().memoizedState]},useMutableSource:El,useSyncExternalStore:xl,useId:ql,unstable_isNewReconciler:!1},ti={readContext:Oo,useCallback:Bl,useContext:Oo,useEffect:Al,useImperativeHandle:Il,useInsertionEffect:Ml,useLayoutEffect:Dl,useMemo:Wl,useReducer:Sl,useRef:Ll,useState:function(){return Sl(wl)},useDebugValue:$l,useDeferredValue:function(e){var t=bl();return null===ul?t.memoizedState=e:Hl(t,ul.memoizedState,e)},useTransition:function(){return[Sl(wl)[0],bl().memoizedState]},useMutableSource:El,useSyncExternalStore:xl,useId:ql,unstable_isNewReconciler:!1};function ni(e,t){if(e&&e.defaultProps){for(var n in t=M({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function ri(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:M({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var ai={isMounted:function(e){return!!(e=e._reactInternals)&&Be(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=es(),a=ts(e),o=Uo(r,a);o.payload=t,void 0!==n&&null!==n&&(o.callback=n),null!==(t=Io(e,o,a))&&(ns(t,e,a,r),$o(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=es(),a=ts(e),o=Uo(r,a);o.tag=1,o.payload=t,void 0!==n&&null!==n&&(o.callback=n),null!==(t=Io(e,o,a))&&(ns(t,e,a,r),$o(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=es(),r=ts(e),a=Uo(n,r);a.tag=2,void 0!==t&&null!==t&&(a.callback=t),null!==(t=Io(e,a,r))&&(ns(t,e,r,n),$o(t,e,r))}};function oi(e,t,n,r,a,o,l){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,o,l):!t.prototype||!t.prototype.isPureReactComponent||(!ur(n,r)||!ur(a,o))}function li(e,t,n){var r=!1,a=Ra,o=t.contextType;return"object"===typeof o&&null!==o?o=Oo(o):(a=La(t)?Pa:_a.current,o=(r=null!==(r=t.contextTypes)&&void 0!==r)?Oa(e,a):Ra),t=new t(n,o),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=ai,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=o),t}function ii(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&ai.enqueueReplaceState(t,t.state,null)}function ui(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},Mo(e);var o=t.contextType;"object"===typeof o&&null!==o?a.context=Oo(o):(o=La(t)?Pa:_a.current,a.context=Oa(e,o)),a.state=e.memoizedState,"function"===typeof(o=t.getDerivedStateFromProps)&&(ri(e,t,o,n),a.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof a.getSnapshotBeforeUpdate||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||(t=a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&ai.enqueueReplaceState(a,a.state,null),Wo(e,n,a,r),a.state=e.memoizedState),"function"===typeof a.componentDidMount&&(e.flags|=4194308)}function si(e,t){try{var n="",r=t;do{n+=$(r),r=r.return}while(r);var a=n}catch(o){a="\nError generating stack: "+o.message+"\n"+o.stack}return{value:e,source:t,stack:a,digest:null}}function ci(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function di(e,t){try{console.error(t.value)}catch(n){setTimeout((function(){throw n}))}}var fi="function"===typeof WeakMap?WeakMap:Map;function pi(e,t,n){(n=Uo(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Hu||(Hu=!0,Vu=r),di(0,t)},n}function hi(e,t,n){(n=Uo(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){di(0,t)}}var o=e.stateNode;return null!==o&&"function"===typeof o.componentDidCatch&&(n.callback=function(){di(0,t),"function"!==typeof r&&(null===qu?qu=new Set([this]):qu.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function mi(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new fi;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=Cs.bind(null,e,t,n),t.then(e,e))}function gi(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function yi(e,t,n,r,a){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Uo(-1,1)).tag=2,Io(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=a,e)}var vi=w.ReactCurrentOwner,bi=!1;function wi(e,t,n,r){t.child=null===e?So(t,null,n,r):ko(t,e.child,n,r)}function ki(e,t,n,r,a){n=n.render;var o=t.ref;return Po(t,a),r=gl(e,t,n,r,o,a),n=yl(),null===e||bi?(ao&&n&&eo(t),t.flags|=1,wi(e,t,r,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Hi(e,t,a))}function Si(e,t,n,r,a){if(null===e){var o=n.type;return"function"!==typeof o||Ls(o)||void 0!==o.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=zs(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=o,Ei(e,t,o,r,a))}if(o=e.child,0===(e.lanes&a)){var l=o.memoizedProps;if((n=null!==(n=n.compare)?n:ur)(l,r)&&e.ref===t.ref)return Hi(e,t,a)}return t.flags|=1,(e=js(o,r)).ref=t.ref,e.return=t,t.child=e}function Ei(e,t,n,r,a){if(null!==e){var o=e.memoizedProps;if(ur(o,r)&&e.ref===t.ref){if(bi=!1,t.pendingProps=r=o,0===(e.lanes&a))return t.lanes=e.lanes,Hi(e,t,a);0!==(131072&e.flags)&&(bi=!0)}}return Ni(e,t,n,r,a)}function xi(e,t,n){var r=t.pendingProps,a=r.children,o=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Na(ju,Lu),Lu|=n;else{if(0===(1073741824&n))return e=null!==o?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Na(ju,Lu),Lu|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==o?o.baseLanes:n,Na(ju,Lu),Lu|=r}else null!==o?(r=o.baseLanes|n,t.memoizedState=null):r=n,Na(ju,Lu),Lu|=r;return wi(e,t,a,n),t.child}function Ci(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Ni(e,t,n,r,a){var o=La(n)?Pa:_a.current;return o=Oa(t,o),Po(t,a),n=gl(e,t,n,r,o,a),r=yl(),null===e||bi?(ao&&r&&eo(t),t.flags|=1,wi(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Hi(e,t,a))}function Ri(e,t,n,r,a){if(La(n)){var o=!0;Aa(t)}else o=!1;if(Po(t,a),null===t.stateNode)Wi(e,t),li(t,n,r),ui(t,n,r,a),r=!0;else if(null===e){var l=t.stateNode,i=t.memoizedProps;l.props=i;var u=l.context,s=n.contextType;"object"===typeof s&&null!==s?s=Oo(s):s=Oa(t,s=La(n)?Pa:_a.current);var c=n.getDerivedStateFromProps,d="function"===typeof c||"function"===typeof l.getSnapshotBeforeUpdate;d||"function"!==typeof l.UNSAFE_componentWillReceiveProps&&"function"!==typeof l.componentWillReceiveProps||(i!==r||u!==s)&&ii(t,l,r,s),Ao=!1;var f=t.memoizedState;l.state=f,Wo(t,r,l,a),u=t.memoizedState,i!==r||f!==u||Ta.current||Ao?("function"===typeof c&&(ri(t,n,c,r),u=t.memoizedState),(i=Ao||oi(t,n,i,r,f,u,s))?(d||"function"!==typeof l.UNSAFE_componentWillMount&&"function"!==typeof l.componentWillMount||("function"===typeof l.componentWillMount&&l.componentWillMount(),"function"===typeof l.UNSAFE_componentWillMount&&l.UNSAFE_componentWillMount()),"function"===typeof l.componentDidMount&&(t.flags|=4194308)):("function"===typeof l.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),l.props=r,l.state=u,l.context=s,r=i):("function"===typeof l.componentDidMount&&(t.flags|=4194308),r=!1)}else{l=t.stateNode,Do(e,t),i=t.memoizedProps,s=t.type===t.elementType?i:ni(t.type,i),l.props=s,d=t.pendingProps,f=l.context,"object"===typeof(u=n.contextType)&&null!==u?u=Oo(u):u=Oa(t,u=La(n)?Pa:_a.current);var p=n.getDerivedStateFromProps;(c="function"===typeof p||"function"===typeof l.getSnapshotBeforeUpdate)||"function"!==typeof l.UNSAFE_componentWillReceiveProps&&"function"!==typeof l.componentWillReceiveProps||(i!==d||f!==u)&&ii(t,l,r,u),Ao=!1,f=t.memoizedState,l.state=f,Wo(t,r,l,a);var h=t.memoizedState;i!==d||f!==h||Ta.current||Ao?("function"===typeof p&&(ri(t,n,p,r),h=t.memoizedState),(s=Ao||oi(t,n,s,r,f,h,u)||!1)?(c||"function"!==typeof l.UNSAFE_componentWillUpdate&&"function"!==typeof l.componentWillUpdate||("function"===typeof l.componentWillUpdate&&l.componentWillUpdate(r,h,u),"function"===typeof l.UNSAFE_componentWillUpdate&&l.UNSAFE_componentWillUpdate(r,h,u)),"function"===typeof l.componentDidUpdate&&(t.flags|=4),"function"===typeof l.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof l.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof l.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=h),l.props=r,l.state=h,l.context=u,r=s):("function"!==typeof l.componentDidUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof l.getSnapshotBeforeUpdate||i===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return _i(e,t,n,r,o,a)}function _i(e,t,n,r,a,o){Ci(e,t);var l=0!==(128&t.flags);if(!r&&!l)return a&&Ma(t,n,!1),Hi(e,t,o);r=t.stateNode,vi.current=t;var i=l&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&l?(t.child=ko(t,e.child,null,o),t.child=ko(t,null,i,o)):wi(e,t,i,o),t.memoizedState=r.state,a&&Ma(t,n,!0),t.child}function Ti(e){var t=e.stateNode;t.pendingContext?za(0,t.pendingContext,t.pendingContext!==t.context):t.context&&za(0,t.context,!1),Yo(e,t.containerInfo)}function Pi(e,t,n,r,a){return ho(),mo(a),t.flags|=256,wi(e,t,n,r),t.child}var Oi,Li,ji,zi,Fi={dehydrated:null,treeContext:null,retryLane:0};function Ai(e){return{baseLanes:e,cachePool:null,transitions:null}}function Mi(e,t,n){var r,a=t.pendingProps,l=el.current,i=!1,u=0!==(128&t.flags);if((r=u)||(r=(null===e||null!==e.memoizedState)&&0!==(2&l)),r?(i=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(l|=1),Na(el,1&l),null===e)return so(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(u=a.children,e=a.fallback,i?(a=t.mode,i=t.child,u={mode:"hidden",children:u},0===(1&a)&&null!==i?(i.childLanes=0,i.pendingProps=u):i=As(u,a,0,null),e=Fs(e,a,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=Ai(n),t.memoizedState=Fi,e):Di(t,u));if(null!==(l=e.memoizedState)&&null!==(r=l.dehydrated))return function(e,t,n,r,a,l,i){if(n)return 256&t.flags?(t.flags&=-257,Ui(e,t,i,r=ci(Error(o(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(l=r.fallback,a=t.mode,r=As({mode:"visible",children:r.children},a,0,null),(l=Fs(l,a,i,null)).flags|=2,r.return=t,l.return=t,r.sibling=l,t.child=r,0!==(1&t.mode)&&ko(t,e.child,null,i),t.child.memoizedState=Ai(i),t.memoizedState=Fi,l);if(0===(1&t.mode))return Ui(e,t,i,null);if("$!"===a.data){if(r=a.nextSibling&&a.nextSibling.dataset)var u=r.dgst;return r=u,Ui(e,t,i,r=ci(l=Error(o(419)),r,void 0))}if(u=0!==(i&e.childLanes),bi||u){if(null!==(r=Tu)){switch(i&-i){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}0!==(a=0!==(a&(r.suspendedLanes|i))?0:a)&&a!==l.retryLane&&(l.retryLane=a,Fo(e,a),ns(r,e,a,-1))}return ms(),Ui(e,t,i,r=ci(Error(o(421))))}return"$?"===a.data?(t.flags|=128,t.child=e.child,t=Rs.bind(null,e),a._reactRetry=t,null):(e=l.treeContext,ro=sa(a.nextSibling),no=t,ao=!0,oo=null,null!==e&&(Qa[Ka++]=Ya,Qa[Ka++]=Xa,Qa[Ka++]=Ja,Ya=e.id,Xa=e.overflow,Ja=t),t=Di(t,r.children),t.flags|=4096,t)}(e,t,u,a,r,l,n);if(i){i=a.fallback,u=t.mode,r=(l=e.child).sibling;var s={mode:"hidden",children:a.children};return 0===(1&u)&&t.child!==l?((a=t.child).childLanes=0,a.pendingProps=s,t.deletions=null):(a=js(l,s)).subtreeFlags=14680064&l.subtreeFlags,null!==r?i=js(r,i):(i=Fs(i,u,n,null)).flags|=2,i.return=t,a.return=t,a.sibling=i,t.child=a,a=i,i=t.child,u=null===(u=e.child.memoizedState)?Ai(n):{baseLanes:u.baseLanes|n,cachePool:null,transitions:u.transitions},i.memoizedState=u,i.childLanes=e.childLanes&~n,t.memoizedState=Fi,a}return e=(i=e.child).sibling,a=js(i,{mode:"visible",children:a.children}),0===(1&t.mode)&&(a.lanes=n),a.return=t,a.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=a,t.memoizedState=null,a}function Di(e,t){return(t=As({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Ui(e,t,n,r){return null!==r&&mo(r),ko(t,e.child,null,n),(e=Di(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Ii(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),To(e.return,t,n)}function $i(e,t,n,r,a){var o=e.memoizedState;null===o?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=a)}function Bi(e,t,n){var r=t.pendingProps,a=r.revealOrder,o=r.tail;if(wi(e,t,r.children,n),0!==(2&(r=el.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Ii(e,n,t);else if(19===e.tag)Ii(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Na(el,r),0===(1&t.mode))t.memoizedState=null;else switch(a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===tl(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),$i(t,!1,a,n,o);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===tl(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}$i(t,!0,n,null,o);break;case"together":$i(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Wi(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Hi(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Au|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(o(153));if(null!==t.child){for(n=js(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=js(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Vi(e,t){if(!ao)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function qi(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=14680064&a.subtreeFlags,r|=14680064&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Qi(e,t,n){var r=t.pendingProps;switch(to(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return qi(t),null;case 1:case 17:return La(t.type)&&ja(),qi(t),null;case 3:return r=t.stateNode,Xo(),Ca(Ta),Ca(_a),rl(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(fo(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==oo&&(ls(oo),oo=null))),Li(e,t),qi(t),null;case 5:Zo(t);var a=Jo(Ko.current);if(n=t.type,null!==e&&null!=t.stateNode)ji(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(o(166));return qi(t),null}if(e=Jo(qo.current),fo(t)){r=t.stateNode,n=t.type;var l=t.memoizedProps;switch(r[fa]=t,r[pa]=l,e=0!==(1&t.mode),n){case"dialog":Ur("cancel",r),Ur("close",r);break;case"iframe":case"object":case"embed":Ur("load",r);break;case"video":case"audio":for(a=0;a<Fr.length;a++)Ur(Fr[a],r);break;case"source":Ur("error",r);break;case"img":case"image":case"link":Ur("error",r),Ur("load",r);break;case"details":Ur("toggle",r);break;case"input":Y(r,l),Ur("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!l.multiple},Ur("invalid",r);break;case"textarea":ae(r,l),Ur("invalid",r)}for(var u in ve(n,l),a=null,l)if(l.hasOwnProperty(u)){var s=l[u];"children"===u?"string"===typeof s?r.textContent!==s&&(!0!==l.suppressHydrationWarning&&Gr(r.textContent,s,e),a=["children",s]):"number"===typeof s&&r.textContent!==""+s&&(!0!==l.suppressHydrationWarning&&Gr(r.textContent,s,e),a=["children",""+s]):i.hasOwnProperty(u)&&null!=s&&"onScroll"===u&&Ur("scroll",r)}switch(n){case"input":q(r),Z(r,l,!0);break;case"textarea":q(r),le(r);break;case"select":case"option":break;default:"function"===typeof l.onClick&&(r.onclick=Zr)}r=a,t.updateQueue=r,null!==r&&(t.flags|=4)}else{u=9===a.nodeType?a:a.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=ie(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=u.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=u.createElement(n,{is:r.is}):(e=u.createElement(n),"select"===n&&(u=e,r.multiple?u.multiple=!0:r.size&&(u.size=r.size))):e=u.createElementNS(e,n),e[fa]=t,e[pa]=r,Oi(e,t,!1,!1),t.stateNode=e;e:{switch(u=be(n,r),n){case"dialog":Ur("cancel",e),Ur("close",e),a=r;break;case"iframe":case"object":case"embed":Ur("load",e),a=r;break;case"video":case"audio":for(a=0;a<Fr.length;a++)Ur(Fr[a],e);a=r;break;case"source":Ur("error",e),a=r;break;case"img":case"image":case"link":Ur("error",e),Ur("load",e),a=r;break;case"details":Ur("toggle",e),a=r;break;case"input":Y(e,r),a=J(e,r),Ur("invalid",e);break;case"option":default:a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=M({},r,{value:void 0}),Ur("invalid",e);break;case"textarea":ae(e,r),a=re(e,r),Ur("invalid",e)}for(l in ve(n,a),s=a)if(s.hasOwnProperty(l)){var c=s[l];"style"===l?ge(e,c):"dangerouslySetInnerHTML"===l?null!=(c=c?c.__html:void 0)&&de(e,c):"children"===l?"string"===typeof c?("textarea"!==n||""!==c)&&fe(e,c):"number"===typeof c&&fe(e,""+c):"suppressContentEditableWarning"!==l&&"suppressHydrationWarning"!==l&&"autoFocus"!==l&&(i.hasOwnProperty(l)?null!=c&&"onScroll"===l&&Ur("scroll",e):null!=c&&b(e,l,c,u))}switch(n){case"input":q(e),Z(e,r,!1);break;case"textarea":q(e),le(e);break;case"option":null!=r.value&&e.setAttribute("value",""+H(r.value));break;case"select":e.multiple=!!r.multiple,null!=(l=r.value)?ne(e,!!r.multiple,l,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof a.onClick&&(e.onclick=Zr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return qi(t),null;case 6:if(e&&null!=t.stateNode)zi(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(o(166));if(n=Jo(Ko.current),Jo(qo.current),fo(t)){if(r=t.stateNode,n=t.memoizedProps,r[fa]=t,(l=r.nodeValue!==n)&&null!==(e=no))switch(e.tag){case 3:Gr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Gr(r.nodeValue,n,0!==(1&e.mode))}l&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[fa]=t,t.stateNode=r}return qi(t),null;case 13:if(Ca(el),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(ao&&null!==ro&&0!==(1&t.mode)&&0===(128&t.flags))po(),ho(),t.flags|=98560,l=!1;else if(l=fo(t),null!==r&&null!==r.dehydrated){if(null===e){if(!l)throw Error(o(318));if(!(l=null!==(l=t.memoizedState)?l.dehydrated:null))throw Error(o(317));l[fa]=t}else ho(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;qi(t),l=!1}else null!==oo&&(ls(oo),oo=null),l=!0;if(!l)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&el.current)?0===zu&&(zu=3):ms())),null!==t.updateQueue&&(t.flags|=4),qi(t),null);case 4:return Xo(),Li(e,t),null===e&&Br(t.stateNode.containerInfo),qi(t),null;case 10:return _o(t.type._context),qi(t),null;case 19:if(Ca(el),null===(l=t.memoizedState))return qi(t),null;if(r=0!==(128&t.flags),null===(u=l.rendering))if(r)Vi(l,!1);else{if(0!==zu||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(u=tl(e))){for(t.flags|=128,Vi(l,!1),null!==(r=u.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(l=n).flags&=14680066,null===(u=l.alternate)?(l.childLanes=0,l.lanes=e,l.child=null,l.subtreeFlags=0,l.memoizedProps=null,l.memoizedState=null,l.updateQueue=null,l.dependencies=null,l.stateNode=null):(l.childLanes=u.childLanes,l.lanes=u.lanes,l.child=u.child,l.subtreeFlags=0,l.deletions=null,l.memoizedProps=u.memoizedProps,l.memoizedState=u.memoizedState,l.updateQueue=u.updateQueue,l.type=u.type,e=u.dependencies,l.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Na(el,1&el.current|2),t.child}e=e.sibling}null!==l.tail&&Xe()>Bu&&(t.flags|=128,r=!0,Vi(l,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=tl(u))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),Vi(l,!0),null===l.tail&&"hidden"===l.tailMode&&!u.alternate&&!ao)return qi(t),null}else 2*Xe()-l.renderingStartTime>Bu&&1073741824!==n&&(t.flags|=128,r=!0,Vi(l,!1),t.lanes=4194304);l.isBackwards?(u.sibling=t.child,t.child=u):(null!==(n=l.last)?n.sibling=u:t.child=u,l.last=u)}return null!==l.tail?(t=l.tail,l.rendering=t,l.tail=t.sibling,l.renderingStartTime=Xe(),t.sibling=null,n=el.current,Na(el,r?1&n|2:1&n),t):(qi(t),null);case 22:case 23:return ds(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Lu)&&(qi(t),6&t.subtreeFlags&&(t.flags|=8192)):qi(t),null;case 24:case 25:return null}throw Error(o(156,t.tag))}function Ki(e,t){switch(to(t),t.tag){case 1:return La(t.type)&&ja(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Xo(),Ca(Ta),Ca(_a),rl(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Zo(t),null;case 13:if(Ca(el),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(o(340));ho()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Ca(el),null;case 4:return Xo(),null;case 10:return _o(t.type._context),null;case 22:case 23:return ds(),null;default:return null}}Oi=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Li=function(){},ji=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,Jo(qo.current);var o,l=null;switch(n){case"input":a=J(e,a),r=J(e,r),l=[];break;case"select":a=M({},a,{value:void 0}),r=M({},r,{value:void 0}),l=[];break;case"textarea":a=re(e,a),r=re(e,r),l=[];break;default:"function"!==typeof a.onClick&&"function"===typeof r.onClick&&(e.onclick=Zr)}for(c in ve(n,r),n=null,a)if(!r.hasOwnProperty(c)&&a.hasOwnProperty(c)&&null!=a[c])if("style"===c){var u=a[c];for(o in u)u.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(i.hasOwnProperty(c)?l||(l=[]):(l=l||[]).push(c,null));for(c in r){var s=r[c];if(u=null!=a?a[c]:void 0,r.hasOwnProperty(c)&&s!==u&&(null!=s||null!=u))if("style"===c)if(u){for(o in u)!u.hasOwnProperty(o)||s&&s.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in s)s.hasOwnProperty(o)&&u[o]!==s[o]&&(n||(n={}),n[o]=s[o])}else n||(l||(l=[]),l.push(c,n)),n=s;else"dangerouslySetInnerHTML"===c?(s=s?s.__html:void 0,u=u?u.__html:void 0,null!=s&&u!==s&&(l=l||[]).push(c,s)):"children"===c?"string"!==typeof s&&"number"!==typeof s||(l=l||[]).push(c,""+s):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(i.hasOwnProperty(c)?(null!=s&&"onScroll"===c&&Ur("scroll",e),l||u===s||(l=[])):(l=l||[]).push(c,s))}n&&(l=l||[]).push("style",n);var c=l;(t.updateQueue=c)&&(t.flags|=4)}},zi=function(e,t,n,r){n!==r&&(t.flags|=4)};var Ji=!1,Yi=!1,Xi="function"===typeof WeakSet?WeakSet:Set,Gi=null;function Zi(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){xs(e,t,r)}else n.current=null}function eu(e,t,n){try{n()}catch(r){xs(e,t,r)}}var tu=!1;function nu(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var a=r=r.next;do{if((a.tag&e)===e){var o=a.destroy;a.destroy=void 0,void 0!==o&&eu(t,n,o)}a=a.next}while(a!==r)}}function ru(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function au(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function ou(e){var t=e.alternate;null!==t&&(e.alternate=null,ou(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[fa],delete t[pa],delete t[ma],delete t[ga],delete t[ya])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function lu(e){return 5===e.tag||3===e.tag||4===e.tag}function iu(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||lu(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function uu(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Zr));else if(4!==r&&null!==(e=e.child))for(uu(e,t,n),e=e.sibling;null!==e;)uu(e,t,n),e=e.sibling}function su(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(su(e,t,n),e=e.sibling;null!==e;)su(e,t,n),e=e.sibling}var cu=null,du=!1;function fu(e,t,n){for(n=n.child;null!==n;)pu(e,t,n),n=n.sibling}function pu(e,t,n){if(ot&&"function"===typeof ot.onCommitFiberUnmount)try{ot.onCommitFiberUnmount(at,n)}catch(i){}switch(n.tag){case 5:Yi||Zi(n,t);case 6:var r=cu,a=du;cu=null,fu(e,t,n),du=a,null!==(cu=r)&&(du?(e=cu,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):cu.removeChild(n.stateNode));break;case 18:null!==cu&&(du?(e=cu,n=n.stateNode,8===e.nodeType?ua(e.parentNode,n):1===e.nodeType&&ua(e,n),Bt(e)):ua(cu,n.stateNode));break;case 4:r=cu,a=du,cu=n.stateNode.containerInfo,du=!0,fu(e,t,n),cu=r,du=a;break;case 0:case 11:case 14:case 15:if(!Yi&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){a=r=r.next;do{var o=a,l=o.destroy;o=o.tag,void 0!==l&&(0!==(2&o)||0!==(4&o))&&eu(n,t,l),a=a.next}while(a!==r)}fu(e,t,n);break;case 1:if(!Yi&&(Zi(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(i){xs(n,t,i)}fu(e,t,n);break;case 21:fu(e,t,n);break;case 22:1&n.mode?(Yi=(r=Yi)||null!==n.memoizedState,fu(e,t,n),Yi=r):fu(e,t,n);break;default:fu(e,t,n)}}function hu(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Xi),t.forEach((function(t){var r=_s.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}function mu(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r];try{var l=e,i=t,u=i;e:for(;null!==u;){switch(u.tag){case 5:cu=u.stateNode,du=!1;break e;case 3:case 4:cu=u.stateNode.containerInfo,du=!0;break e}u=u.return}if(null===cu)throw Error(o(160));pu(l,i,a),cu=null,du=!1;var s=a.alternate;null!==s&&(s.return=null),a.return=null}catch(c){xs(a,t,c)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)gu(t,e),t=t.sibling}function gu(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(mu(t,e),yu(e),4&r){try{nu(3,e,e.return),ru(3,e)}catch(g){xs(e,e.return,g)}try{nu(5,e,e.return)}catch(g){xs(e,e.return,g)}}break;case 1:mu(t,e),yu(e),512&r&&null!==n&&Zi(n,n.return);break;case 5:if(mu(t,e),yu(e),512&r&&null!==n&&Zi(n,n.return),32&e.flags){var a=e.stateNode;try{fe(a,"")}catch(g){xs(e,e.return,g)}}if(4&r&&null!=(a=e.stateNode)){var l=e.memoizedProps,i=null!==n?n.memoizedProps:l,u=e.type,s=e.updateQueue;if(e.updateQueue=null,null!==s)try{"input"===u&&"radio"===l.type&&null!=l.name&&X(a,l),be(u,i);var c=be(u,l);for(i=0;i<s.length;i+=2){var d=s[i],f=s[i+1];"style"===d?ge(a,f):"dangerouslySetInnerHTML"===d?de(a,f):"children"===d?fe(a,f):b(a,d,f,c)}switch(u){case"input":G(a,l);break;case"textarea":oe(a,l);break;case"select":var p=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!l.multiple;var h=l.value;null!=h?ne(a,!!l.multiple,h,!1):p!==!!l.multiple&&(null!=l.defaultValue?ne(a,!!l.multiple,l.defaultValue,!0):ne(a,!!l.multiple,l.multiple?[]:"",!1))}a[pa]=l}catch(g){xs(e,e.return,g)}}break;case 6:if(mu(t,e),yu(e),4&r){if(null===e.stateNode)throw Error(o(162));a=e.stateNode,l=e.memoizedProps;try{a.nodeValue=l}catch(g){xs(e,e.return,g)}}break;case 3:if(mu(t,e),yu(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Bt(t.containerInfo)}catch(g){xs(e,e.return,g)}break;case 4:default:mu(t,e),yu(e);break;case 13:mu(t,e),yu(e),8192&(a=e.child).flags&&(l=null!==a.memoizedState,a.stateNode.isHidden=l,!l||null!==a.alternate&&null!==a.alternate.memoizedState||($u=Xe())),4&r&&hu(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Yi=(c=Yi)||d,mu(t,e),Yi=c):mu(t,e),yu(e),8192&r){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!d&&0!==(1&e.mode))for(Gi=e,d=e.child;null!==d;){for(f=Gi=d;null!==Gi;){switch(h=(p=Gi).child,p.tag){case 0:case 11:case 14:case 15:nu(4,p,p.return);break;case 1:Zi(p,p.return);var m=p.stateNode;if("function"===typeof m.componentWillUnmount){r=p,n=p.return;try{t=r,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(g){xs(r,n,g)}}break;case 5:Zi(p,p.return);break;case 22:if(null!==p.memoizedState){ku(f);continue}}null!==h?(h.return=p,Gi=h):ku(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{a=f.stateNode,c?"function"===typeof(l=a.style).setProperty?l.setProperty("display","none","important"):l.display="none":(u=f.stateNode,i=void 0!==(s=f.memoizedProps.style)&&null!==s&&s.hasOwnProperty("display")?s.display:null,u.style.display=me("display",i))}catch(g){xs(e,e.return,g)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(g){xs(e,e.return,g)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:mu(t,e),yu(e),4&r&&hu(e);case 21:}}function yu(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(lu(n)){var r=n;break e}n=n.return}throw Error(o(160))}switch(r.tag){case 5:var a=r.stateNode;32&r.flags&&(fe(a,""),r.flags&=-33),su(e,iu(e),a);break;case 3:case 4:var l=r.stateNode.containerInfo;uu(e,iu(e),l);break;default:throw Error(o(161))}}catch(i){xs(e,e.return,i)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function vu(e,t,n){Gi=e,bu(e,t,n)}function bu(e,t,n){for(var r=0!==(1&e.mode);null!==Gi;){var a=Gi,o=a.child;if(22===a.tag&&r){var l=null!==a.memoizedState||Ji;if(!l){var i=a.alternate,u=null!==i&&null!==i.memoizedState||Yi;i=Ji;var s=Yi;if(Ji=l,(Yi=u)&&!s)for(Gi=a;null!==Gi;)u=(l=Gi).child,22===l.tag&&null!==l.memoizedState?Su(a):null!==u?(u.return=l,Gi=u):Su(a);for(;null!==o;)Gi=o,bu(o,t,n),o=o.sibling;Gi=a,Ji=i,Yi=s}wu(e)}else 0!==(8772&a.subtreeFlags)&&null!==o?(o.return=a,Gi=o):wu(e)}}function wu(e){for(;null!==Gi;){var t=Gi;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Yi||ru(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Yi)if(null===n)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:ni(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var l=t.updateQueue;null!==l&&Ho(t,l,r);break;case 3:var i=t.updateQueue;if(null!==i){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Ho(t,i,n)}break;case 5:var u=t.stateNode;if(null===n&&4&t.flags){n=u;var s=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":s.autoFocus&&n.focus();break;case"img":s.src&&(n.src=s.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var d=c.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Bt(f)}}}break;default:throw Error(o(163))}Yi||512&t.flags&&au(t)}catch(p){xs(t,t.return,p)}}if(t===e){Gi=null;break}if(null!==(n=t.sibling)){n.return=t.return,Gi=n;break}Gi=t.return}}function ku(e){for(;null!==Gi;){var t=Gi;if(t===e){Gi=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Gi=n;break}Gi=t.return}}function Su(e){for(;null!==Gi;){var t=Gi;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ru(4,t)}catch(u){xs(t,n,u)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var a=t.return;try{r.componentDidMount()}catch(u){xs(t,a,u)}}var o=t.return;try{au(t)}catch(u){xs(t,o,u)}break;case 5:var l=t.return;try{au(t)}catch(u){xs(t,l,u)}}}catch(u){xs(t,t.return,u)}if(t===e){Gi=null;break}var i=t.sibling;if(null!==i){i.return=t.return,Gi=i;break}Gi=t.return}}var Eu,xu=Math.ceil,Cu=w.ReactCurrentDispatcher,Nu=w.ReactCurrentOwner,Ru=w.ReactCurrentBatchConfig,_u=0,Tu=null,Pu=null,Ou=0,Lu=0,ju=xa(0),zu=0,Fu=null,Au=0,Mu=0,Du=0,Uu=null,Iu=null,$u=0,Bu=1/0,Wu=null,Hu=!1,Vu=null,qu=null,Qu=!1,Ku=null,Ju=0,Yu=0,Xu=null,Gu=-1,Zu=0;function es(){return 0!==(6&_u)?Xe():-1!==Gu?Gu:Gu=Xe()}function ts(e){return 0===(1&e.mode)?1:0!==(2&_u)&&0!==Ou?Ou&-Ou:null!==go.transition?(0===Zu&&(Zu=mt()),Zu):0!==(e=bt)?e:e=void 0===(e=window.event)?16:Yt(e.type)}function ns(e,t,n,r){if(50<Yu)throw Yu=0,Xu=null,Error(o(185));yt(e,n,r),0!==(2&_u)&&e===Tu||(e===Tu&&(0===(2&_u)&&(Mu|=n),4===zu&&is(e,Ou)),rs(e,r),1===n&&0===_u&&0===(1&t.mode)&&(Bu=Xe()+500,Ua&&Ba()))}function rs(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,o=e.pendingLanes;0<o;){var l=31-lt(o),i=1<<l,u=a[l];-1===u?0!==(i&n)&&0===(i&r)||(a[l]=pt(i,t)):u<=t&&(e.expiredLanes|=i),o&=~i}}(e,t);var r=ft(e,e===Tu?Ou:0);if(0===r)null!==n&&Ke(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Ke(n),1===t)0===e.tag?function(e){Ua=!0,$a(e)}(us.bind(null,e)):$a(us.bind(null,e)),la((function(){0===(6&_u)&&Ba()})),n=null;else{switch(wt(r)){case 1:n=Ze;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=Ts(n,as.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function as(e,t){if(Gu=-1,Zu=0,0!==(6&_u))throw Error(o(327));var n=e.callbackNode;if(Ss()&&e.callbackNode!==n)return null;var r=ft(e,e===Tu?Ou:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=gs(e,r);else{t=r;var a=_u;_u|=2;var l=hs();for(Tu===e&&Ou===t||(Wu=null,Bu=Xe()+500,fs(e,t));;)try{vs();break}catch(u){ps(e,u)}Ro(),Cu.current=l,_u=a,null!==Pu?t=0:(Tu=null,Ou=0,t=zu)}if(0!==t){if(2===t&&(0!==(a=ht(e))&&(r=a,t=os(e,a))),1===t)throw n=Fu,fs(e,0),is(e,r),rs(e,Xe()),n;if(6===t)is(e,r);else{if(a=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],o=a.getSnapshot;a=a.value;try{if(!ir(o(),a))return!1}catch(i){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(a)&&(2===(t=gs(e,r))&&(0!==(l=ht(e))&&(r=l,t=os(e,l))),1===t))throw n=Fu,fs(e,0),is(e,r),rs(e,Xe()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(o(345));case 2:case 5:ks(e,Iu,Wu);break;case 3:if(is(e,r),(130023424&r)===r&&10<(t=$u+500-Xe())){if(0!==ft(e,0))break;if(((a=e.suspendedLanes)&r)!==r){es(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=ra(ks.bind(null,e,Iu,Wu),t);break}ks(e,Iu,Wu);break;case 4:if(is(e,r),(4194240&r)===r)break;for(t=e.eventTimes,a=-1;0<r;){var i=31-lt(r);l=1<<i,(i=t[i])>a&&(a=i),r&=~l}if(r=a,10<(r=(120>(r=Xe()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*xu(r/1960))-r)){e.timeoutHandle=ra(ks.bind(null,e,Iu,Wu),r);break}ks(e,Iu,Wu);break;default:throw Error(o(329))}}}return rs(e,Xe()),e.callbackNode===n?as.bind(null,e):null}function os(e,t){var n=Uu;return e.current.memoizedState.isDehydrated&&(fs(e,t).flags|=256),2!==(e=gs(e,t))&&(t=Iu,Iu=n,null!==t&&ls(t)),e}function ls(e){null===Iu?Iu=e:Iu.push.apply(Iu,e)}function is(e,t){for(t&=~Du,t&=~Mu,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-lt(t),r=1<<n;e[n]=-1,t&=~r}}function us(e){if(0!==(6&_u))throw Error(o(327));Ss();var t=ft(e,0);if(0===(1&t))return rs(e,Xe()),null;var n=gs(e,t);if(0!==e.tag&&2===n){var r=ht(e);0!==r&&(t=r,n=os(e,r))}if(1===n)throw n=Fu,fs(e,0),is(e,t),rs(e,Xe()),n;if(6===n)throw Error(o(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,ks(e,Iu,Wu),rs(e,Xe()),null}function ss(e,t){var n=_u;_u|=1;try{return e(t)}finally{0===(_u=n)&&(Bu=Xe()+500,Ua&&Ba())}}function cs(e){null!==Ku&&0===Ku.tag&&0===(6&_u)&&Ss();var t=_u;_u|=1;var n=Ru.transition,r=bt;try{if(Ru.transition=null,bt=1,e)return e()}finally{bt=r,Ru.transition=n,0===(6&(_u=t))&&Ba()}}function ds(){Lu=ju.current,Ca(ju)}function fs(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,aa(n)),null!==Pu)for(n=Pu.return;null!==n;){var r=n;switch(to(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&ja();break;case 3:Xo(),Ca(Ta),Ca(_a),rl();break;case 5:Zo(r);break;case 4:Xo();break;case 13:case 19:Ca(el);break;case 10:_o(r.type._context);break;case 22:case 23:ds()}n=n.return}if(Tu=e,Pu=e=js(e.current,null),Ou=Lu=t,zu=0,Fu=null,Du=Mu=Au=0,Iu=Uu=null,null!==Lo){for(t=0;t<Lo.length;t++)if(null!==(r=(n=Lo[t]).interleaved)){n.interleaved=null;var a=r.next,o=n.pending;if(null!==o){var l=o.next;o.next=a,r.next=l}n.pending=r}Lo=null}return e}function ps(e,t){for(;;){var n=Pu;try{if(Ro(),al.current=Gl,cl){for(var r=il.memoizedState;null!==r;){var a=r.queue;null!==a&&(a.pending=null),r=r.next}cl=!1}if(ll=0,sl=ul=il=null,dl=!1,fl=0,Nu.current=null,null===n||null===n.return){zu=1,Fu=t,Pu=null;break}e:{var l=e,i=n.return,u=n,s=t;if(t=Ou,u.flags|=32768,null!==s&&"object"===typeof s&&"function"===typeof s.then){var c=s,d=u,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var h=gi(i);if(null!==h){h.flags&=-257,yi(h,i,u,0,t),1&h.mode&&mi(l,c,t),s=c;var m=(t=h).updateQueue;if(null===m){var g=new Set;g.add(s),t.updateQueue=g}else m.add(s);break e}if(0===(1&t)){mi(l,c,t),ms();break e}s=Error(o(426))}else if(ao&&1&u.mode){var y=gi(i);if(null!==y){0===(65536&y.flags)&&(y.flags|=256),yi(y,i,u,0,t),mo(si(s,u));break e}}l=s=si(s,u),4!==zu&&(zu=2),null===Uu?Uu=[l]:Uu.push(l),l=i;do{switch(l.tag){case 3:l.flags|=65536,t&=-t,l.lanes|=t,Bo(l,pi(0,s,t));break e;case 1:u=s;var v=l.type,b=l.stateNode;if(0===(128&l.flags)&&("function"===typeof v.getDerivedStateFromError||null!==b&&"function"===typeof b.componentDidCatch&&(null===qu||!qu.has(b)))){l.flags|=65536,t&=-t,l.lanes|=t,Bo(l,hi(l,u,t));break e}}l=l.return}while(null!==l)}ws(n)}catch(w){t=w,Pu===n&&null!==n&&(Pu=n=n.return);continue}break}}function hs(){var e=Cu.current;return Cu.current=Gl,null===e?Gl:e}function ms(){0!==zu&&3!==zu&&2!==zu||(zu=4),null===Tu||0===(268435455&Au)&&0===(268435455&Mu)||is(Tu,Ou)}function gs(e,t){var n=_u;_u|=2;var r=hs();for(Tu===e&&Ou===t||(Wu=null,fs(e,t));;)try{ys();break}catch(a){ps(e,a)}if(Ro(),_u=n,Cu.current=r,null!==Pu)throw Error(o(261));return Tu=null,Ou=0,zu}function ys(){for(;null!==Pu;)bs(Pu)}function vs(){for(;null!==Pu&&!Je();)bs(Pu)}function bs(e){var t=Eu(e.alternate,e,Lu);e.memoizedProps=e.pendingProps,null===t?ws(e):Pu=t,Nu.current=null}function ws(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=Qi(n,t,Lu)))return void(Pu=n)}else{if(null!==(n=Ki(n,t)))return n.flags&=32767,void(Pu=n);if(null===e)return zu=6,void(Pu=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Pu=t);Pu=t=e}while(null!==t);0===zu&&(zu=5)}function ks(e,t,n){var r=bt,a=Ru.transition;try{Ru.transition=null,bt=1,function(e,t,n,r){do{Ss()}while(null!==Ku);if(0!==(6&_u))throw Error(o(327));n=e.finishedWork;var a=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(o(177));e.callbackNode=null,e.callbackPriority=0;var l=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-lt(n),o=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~o}}(e,l),e===Tu&&(Pu=Tu=null,Ou=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||Qu||(Qu=!0,Ts(tt,(function(){return Ss(),null}))),l=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||l){l=Ru.transition,Ru.transition=null;var i=bt;bt=1;var u=_u;_u|=4,Nu.current=null,function(e,t){if(ea=Ht,pr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,l=r.focusNode;r=r.focusOffset;try{n.nodeType,l.nodeType}catch(k){n=null;break e}var i=0,u=-1,s=-1,c=0,d=0,f=e,p=null;t:for(;;){for(var h;f!==n||0!==a&&3!==f.nodeType||(u=i+a),f!==l||0!==r&&3!==f.nodeType||(s=i+r),3===f.nodeType&&(i+=f.nodeValue.length),null!==(h=f.firstChild);)p=f,f=h;for(;;){if(f===e)break t;if(p===n&&++c===a&&(u=i),p===l&&++d===r&&(s=i),null!==(h=f.nextSibling))break;p=(f=p).parentNode}f=h}n=-1===u||-1===s?null:{start:u,end:s}}else n=null}n=n||{start:0,end:0}}else n=null;for(ta={focusedElem:e,selectionRange:n},Ht=!1,Gi=t;null!==Gi;)if(e=(t=Gi).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Gi=e;else for(;null!==Gi;){t=Gi;try{var m=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==m){var g=m.memoizedProps,y=m.memoizedState,v=t.stateNode,b=v.getSnapshotBeforeUpdate(t.elementType===t.type?g:ni(t.type,g),y);v.__reactInternalSnapshotBeforeUpdate=b}break;case 3:var w=t.stateNode.containerInfo;1===w.nodeType?w.textContent="":9===w.nodeType&&w.documentElement&&w.removeChild(w.documentElement);break;default:throw Error(o(163))}}catch(k){xs(t,t.return,k)}if(null!==(e=t.sibling)){e.return=t.return,Gi=e;break}Gi=t.return}m=tu,tu=!1}(e,n),gu(n,e),hr(ta),Ht=!!ea,ta=ea=null,e.current=n,vu(n,e,a),Ye(),_u=u,bt=i,Ru.transition=l}else e.current=n;if(Qu&&(Qu=!1,Ku=e,Ju=a),l=e.pendingLanes,0===l&&(qu=null),function(e){if(ot&&"function"===typeof ot.onCommitFiberRoot)try{ot.onCommitFiberRoot(at,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),rs(e,Xe()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)a=t[n],r(a.value,{componentStack:a.stack,digest:a.digest});if(Hu)throw Hu=!1,e=Vu,Vu=null,e;0!==(1&Ju)&&0!==e.tag&&Ss(),l=e.pendingLanes,0!==(1&l)?e===Xu?Yu++:(Yu=0,Xu=e):Yu=0,Ba()}(e,t,n,r)}finally{Ru.transition=a,bt=r}return null}function Ss(){if(null!==Ku){var e=wt(Ju),t=Ru.transition,n=bt;try{if(Ru.transition=null,bt=16>e?16:e,null===Ku)var r=!1;else{if(e=Ku,Ku=null,Ju=0,0!==(6&_u))throw Error(o(331));var a=_u;for(_u|=4,Gi=e.current;null!==Gi;){var l=Gi,i=l.child;if(0!==(16&Gi.flags)){var u=l.deletions;if(null!==u){for(var s=0;s<u.length;s++){var c=u[s];for(Gi=c;null!==Gi;){var d=Gi;switch(d.tag){case 0:case 11:case 15:nu(8,d,l)}var f=d.child;if(null!==f)f.return=d,Gi=f;else for(;null!==Gi;){var p=(d=Gi).sibling,h=d.return;if(ou(d),d===c){Gi=null;break}if(null!==p){p.return=h,Gi=p;break}Gi=h}}}var m=l.alternate;if(null!==m){var g=m.child;if(null!==g){m.child=null;do{var y=g.sibling;g.sibling=null,g=y}while(null!==g)}}Gi=l}}if(0!==(2064&l.subtreeFlags)&&null!==i)i.return=l,Gi=i;else e:for(;null!==Gi;){if(0!==(2048&(l=Gi).flags))switch(l.tag){case 0:case 11:case 15:nu(9,l,l.return)}var v=l.sibling;if(null!==v){v.return=l.return,Gi=v;break e}Gi=l.return}}var b=e.current;for(Gi=b;null!==Gi;){var w=(i=Gi).child;if(0!==(2064&i.subtreeFlags)&&null!==w)w.return=i,Gi=w;else e:for(i=b;null!==Gi;){if(0!==(2048&(u=Gi).flags))try{switch(u.tag){case 0:case 11:case 15:ru(9,u)}}catch(S){xs(u,u.return,S)}if(u===i){Gi=null;break e}var k=u.sibling;if(null!==k){k.return=u.return,Gi=k;break e}Gi=u.return}}if(_u=a,Ba(),ot&&"function"===typeof ot.onPostCommitFiberRoot)try{ot.onPostCommitFiberRoot(at,e)}catch(S){}r=!0}return r}finally{bt=n,Ru.transition=t}}return!1}function Es(e,t,n){e=Io(e,t=pi(0,t=si(n,t),1),1),t=es(),null!==e&&(yt(e,1,t),rs(e,t))}function xs(e,t,n){if(3===e.tag)Es(e,e,n);else for(;null!==t;){if(3===t.tag){Es(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===qu||!qu.has(r))){t=Io(t,e=hi(t,e=si(n,e),1),1),e=es(),null!==t&&(yt(t,1,e),rs(t,e));break}}t=t.return}}function Cs(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=es(),e.pingedLanes|=e.suspendedLanes&n,Tu===e&&(Ou&n)===n&&(4===zu||3===zu&&(130023424&Ou)===Ou&&500>Xe()-$u?fs(e,0):Du|=n),rs(e,t)}function Ns(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ct,0===(130023424&(ct<<=1))&&(ct=4194304)));var n=es();null!==(e=Fo(e,t))&&(yt(e,t,n),rs(e,n))}function Rs(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Ns(e,n)}function _s(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(o(314))}null!==r&&r.delete(t),Ns(e,n)}function Ts(e,t){return Qe(e,t)}function Ps(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Os(e,t,n,r){return new Ps(e,t,n,r)}function Ls(e){return!(!(e=e.prototype)||!e.isReactComponent)}function js(e,t){var n=e.alternate;return null===n?((n=Os(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function zs(e,t,n,r,a,l){var i=2;if(r=e,"function"===typeof e)Ls(e)&&(i=1);else if("string"===typeof e)i=5;else e:switch(e){case E:return Fs(n.children,a,l,t);case x:i=8,a|=8;break;case C:return(e=Os(12,n,t,2|a)).elementType=C,e.lanes=l,e;case T:return(e=Os(13,n,t,a)).elementType=T,e.lanes=l,e;case P:return(e=Os(19,n,t,a)).elementType=P,e.lanes=l,e;case j:return As(n,a,l,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case N:i=10;break e;case R:i=9;break e;case _:i=11;break e;case O:i=14;break e;case L:i=16,r=null;break e}throw Error(o(130,null==e?e:typeof e,""))}return(t=Os(i,n,t,a)).elementType=e,t.type=r,t.lanes=l,t}function Fs(e,t,n,r){return(e=Os(7,e,r,t)).lanes=n,e}function As(e,t,n,r){return(e=Os(22,e,r,t)).elementType=j,e.lanes=n,e.stateNode={isHidden:!1},e}function Ms(e,t,n){return(e=Os(6,e,null,t)).lanes=n,e}function Ds(e,t,n){return(t=Os(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Us(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gt(0),this.expirationTimes=gt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gt(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function Is(e,t,n,r,a,o,l,i,u){return e=new Us(e,t,n,i,u),1===t?(t=1,!0===o&&(t|=8)):t=0,o=Os(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Mo(o),e}function $s(e){if(!e)return Ra;e:{if(Be(e=e._reactInternals)!==e||1!==e.tag)throw Error(o(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(La(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(o(171))}if(1===e.tag){var n=e.type;if(La(n))return Fa(e,n,t)}return t}function Bs(e,t,n,r,a,o,l,i,u){return(e=Is(n,r,!0,e,0,o,0,i,u)).context=$s(null),n=e.current,(o=Uo(r=es(),a=ts(n))).callback=void 0!==t&&null!==t?t:null,Io(n,o,a),e.current.lanes=a,yt(e,a,r),rs(e,r),e}function Ws(e,t,n,r){var a=t.current,o=es(),l=ts(a);return n=$s(n),null===t.context?t.context=n:t.pendingContext=n,(t=Uo(o,l)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Io(a,t,l))&&(ns(e,a,l,o),$o(e,a,l)),l}function Hs(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function Vs(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function qs(e,t){Vs(e,t),(e=e.alternate)&&Vs(e,t)}Eu=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Ta.current)bi=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return bi=!1,function(e,t,n){switch(t.tag){case 3:Ti(t),ho();break;case 5:Go(t);break;case 1:La(t.type)&&Aa(t);break;case 4:Yo(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;Na(Eo,r._currentValue),r._currentValue=a;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Na(el,1&el.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Mi(e,t,n):(Na(el,1&el.current),null!==(e=Hi(e,t,n))?e.sibling:null);Na(el,1&el.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Bi(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),Na(el,el.current),r)break;return null;case 22:case 23:return t.lanes=0,xi(e,t,n)}return Hi(e,t,n)}(e,t,n);bi=0!==(131072&e.flags)}else bi=!1,ao&&0!==(1048576&t.flags)&&Za(t,qa,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Wi(e,t),e=t.pendingProps;var a=Oa(t,_a.current);Po(t,n),a=gl(null,t,r,e,a,n);var l=yl();return t.flags|=1,"object"===typeof a&&null!==a&&"function"===typeof a.render&&void 0===a.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,La(r)?(l=!0,Aa(t)):l=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,Mo(t),a.updater=ai,t.stateNode=a,a._reactInternals=t,ui(t,r,e,n),t=_i(null,t,r,!0,l,n)):(t.tag=0,ao&&l&&eo(t),wi(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Wi(e,t),e=t.pendingProps,r=(a=r._init)(r._payload),t.type=r,a=t.tag=function(e){if("function"===typeof e)return Ls(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===_)return 11;if(e===O)return 14}return 2}(r),e=ni(r,e),a){case 0:t=Ni(null,t,r,e,n);break e;case 1:t=Ri(null,t,r,e,n);break e;case 11:t=ki(null,t,r,e,n);break e;case 14:t=Si(null,t,r,ni(r.type,e),n);break e}throw Error(o(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,Ni(e,t,r,a=t.elementType===r?a:ni(r,a),n);case 1:return r=t.type,a=t.pendingProps,Ri(e,t,r,a=t.elementType===r?a:ni(r,a),n);case 3:e:{if(Ti(t),null===e)throw Error(o(387));r=t.pendingProps,a=(l=t.memoizedState).element,Do(e,t),Wo(t,r,null,n);var i=t.memoizedState;if(r=i.element,l.isDehydrated){if(l={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=l,t.memoizedState=l,256&t.flags){t=Pi(e,t,r,n,a=si(Error(o(423)),t));break e}if(r!==a){t=Pi(e,t,r,n,a=si(Error(o(424)),t));break e}for(ro=sa(t.stateNode.containerInfo.firstChild),no=t,ao=!0,oo=null,n=So(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(ho(),r===a){t=Hi(e,t,n);break e}wi(e,t,r,n)}t=t.child}return t;case 5:return Go(t),null===e&&so(t),r=t.type,a=t.pendingProps,l=null!==e?e.memoizedProps:null,i=a.children,na(r,a)?i=null:null!==l&&na(r,l)&&(t.flags|=32),Ci(e,t),wi(e,t,i,n),t.child;case 6:return null===e&&so(t),null;case 13:return Mi(e,t,n);case 4:return Yo(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=ko(t,null,r,n):wi(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,ki(e,t,r,a=t.elementType===r?a:ni(r,a),n);case 7:return wi(e,t,t.pendingProps,n),t.child;case 8:case 12:return wi(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,l=t.memoizedProps,i=a.value,Na(Eo,r._currentValue),r._currentValue=i,null!==l)if(ir(l.value,i)){if(l.children===a.children&&!Ta.current){t=Hi(e,t,n);break e}}else for(null!==(l=t.child)&&(l.return=t);null!==l;){var u=l.dependencies;if(null!==u){i=l.child;for(var s=u.firstContext;null!==s;){if(s.context===r){if(1===l.tag){(s=Uo(-1,n&-n)).tag=2;var c=l.updateQueue;if(null!==c){var d=(c=c.shared).pending;null===d?s.next=s:(s.next=d.next,d.next=s),c.pending=s}}l.lanes|=n,null!==(s=l.alternate)&&(s.lanes|=n),To(l.return,n,t),u.lanes|=n;break}s=s.next}}else if(10===l.tag)i=l.type===t.type?null:l.child;else if(18===l.tag){if(null===(i=l.return))throw Error(o(341));i.lanes|=n,null!==(u=i.alternate)&&(u.lanes|=n),To(i,n,t),i=l.sibling}else i=l.child;if(null!==i)i.return=l;else for(i=l;null!==i;){if(i===t){i=null;break}if(null!==(l=i.sibling)){l.return=i.return,i=l;break}i=i.return}l=i}wi(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,Po(t,n),r=r(a=Oo(a)),t.flags|=1,wi(e,t,r,n),t.child;case 14:return a=ni(r=t.type,t.pendingProps),Si(e,t,r,a=ni(r.type,a),n);case 15:return Ei(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:ni(r,a),Wi(e,t),t.tag=1,La(r)?(e=!0,Aa(t)):e=!1,Po(t,n),li(t,r,a),ui(t,r,a,n),_i(null,t,r,!0,e,n);case 19:return Bi(e,t,n);case 22:return xi(e,t,n)}throw Error(o(156,t.tag))};var Qs="function"===typeof reportError?reportError:function(e){console.error(e)};function Ks(e){this._internalRoot=e}function Js(e){this._internalRoot=e}function Ys(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Xs(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Gs(){}function Zs(e,t,n,r,a){var o=n._reactRootContainer;if(o){var l=o;if("function"===typeof a){var i=a;a=function(){var e=Hs(l);i.call(e)}}Ws(t,l,e,a)}else l=function(e,t,n,r,a){if(a){if("function"===typeof r){var o=r;r=function(){var e=Hs(l);o.call(e)}}var l=Bs(t,r,e,0,null,!1,0,"",Gs);return e._reactRootContainer=l,e[ha]=l.current,Br(8===e.nodeType?e.parentNode:e),cs(),l}for(;a=e.lastChild;)e.removeChild(a);if("function"===typeof r){var i=r;r=function(){var e=Hs(u);i.call(e)}}var u=Is(e,0,!1,null,0,!1,0,"",Gs);return e._reactRootContainer=u,e[ha]=u.current,Br(8===e.nodeType?e.parentNode:e),cs((function(){Ws(t,u,n,r)})),u}(n,t,e,a,r);return Hs(l)}Js.prototype.render=Ks.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(o(409));Ws(e,t,null,null)},Js.prototype.unmount=Ks.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;cs((function(){Ws(null,e,null,null)})),t[ha]=null}},Js.prototype.unstable_scheduleHydration=function(e){if(e){var t=xt();e={blockedOn:null,target:e,priority:t};for(var n=0;n<jt.length&&0!==t&&t<jt[n].priority;n++);jt.splice(n,0,e),0===n&&Mt(e)}},kt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(vt(t,1|n),rs(t,Xe()),0===(6&_u)&&(Bu=Xe()+500,Ba()))}break;case 13:cs((function(){var t=Fo(e,1);if(null!==t){var n=es();ns(t,e,1,n)}})),qs(e,1)}},St=function(e){if(13===e.tag){var t=Fo(e,134217728);if(null!==t)ns(t,e,134217728,es());qs(e,134217728)}},Et=function(e){if(13===e.tag){var t=ts(e),n=Fo(e,t);if(null!==n)ns(n,e,t,es());qs(e,t)}},xt=function(){return bt},Ct=function(e,t){var n=bt;try{return bt=e,t()}finally{bt=n}},Se=function(e,t,n){switch(t){case"input":if(G(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=ka(r);if(!a)throw Error(o(90));Q(r),G(r,a)}}}break;case"textarea":oe(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},_e=ss,Te=cs;var ec={usingClientEntryPoint:!1,Events:[ba,wa,ka,Ne,Re,ss]},tc={findFiberByHostInstance:va,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nc={bundleType:tc.bundleType,version:tc.version,rendererPackageName:tc.rendererPackageName,rendererConfig:tc.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:w.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=Ve(e))?null:e.stateNode},findFiberByHostInstance:tc.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var rc=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!rc.isDisabled&&rc.supportsFiber)try{at=rc.inject(nc),ot=rc}catch(ce){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ec,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Ys(t))throw Error(o(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:S,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Ys(e))throw Error(o(299));var n=!1,r="",a=Qs;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(a=t.onRecoverableError)),t=Is(e,1,!1,null,0,n,0,r,a),e[ha]=t.current,Br(8===e.nodeType?e.parentNode:e),new Ks(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(o(188));throw e=Object.keys(e).join(","),Error(o(268,e))}return e=null===(e=Ve(t))?null:e.stateNode},t.flushSync=function(e){return cs(e)},t.hydrate=function(e,t,n){if(!Xs(t))throw Error(o(200));return Zs(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Ys(e))throw Error(o(405));var r=null!=n&&n.hydratedSources||null,a=!1,l="",i=Qs;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(a=!0),void 0!==n.identifierPrefix&&(l=n.identifierPrefix),void 0!==n.onRecoverableError&&(i=n.onRecoverableError)),t=Bs(t,null,e,1,null!=n?n:null,a,0,l,i),e[ha]=t.current,Br(e),r)for(e=0;e<r.length;e++)a=(a=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new Js(t)},t.render=function(e,t,n){if(!Xs(t))throw Error(o(200));return Zs(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Xs(e))throw Error(o(40));return!!e._reactRootContainer&&(cs((function(){Zs(null,null,e,!1,(function(){e._reactRootContainer=null,e[ha]=null}))})),!0)},t.unstable_batchedUpdates=ss,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Xs(n))throw Error(o(200));if(null==e||void 0===e._reactInternals)throw Error(o(38));return Zs(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},391:(e,t,n)=>{var r=n(950);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},950:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(730)},358:(e,t)=>{const n=/^[\u0021-\u003A\u003C\u003E-\u007E]+$/,r=/^[\u0021-\u003A\u003C-\u007E]*$/,a=/^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i,o=/^[\u0020-\u003A\u003D-\u007E]*$/,l=Object.prototype.toString,i=(()=>{const e=function(){};return e.prototype=Object.create(null),e})();function u(e,t,n){do{const n=e.charCodeAt(t);if(32!==n&&9!==n)return t}while(++t<n);return n}function s(e,t,n){for(;t>n;){const n=e.charCodeAt(--t);if(32!==n&&9!==n)return t+1}return n}function c(e){if(-1===e.indexOf("%"))return e;try{return decodeURIComponent(e)}catch(t){return e}}},153:(e,t,n)=>{var r=n(43),a=Symbol.for("react.element"),o=Symbol.for("react.fragment"),l=Object.prototype.hasOwnProperty,i=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,u={key:!0,ref:!0,__self:!0,__source:!0};function s(e,t,n){var r,o={},s=null,c=null;for(r in void 0!==n&&(s=""+n),void 0!==t.key&&(s=""+t.key),void 0!==t.ref&&(c=t.ref),t)l.call(t,r)&&!u.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===o[r]&&(o[r]=t[r]);return{$$typeof:a,type:e,key:s,ref:c,props:o,_owner:i.current}}t.Fragment=o,t.jsx=s,t.jsxs=s},202:(e,t)=>{var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),l=Symbol.for("react.profiler"),i=Symbol.for("react.provider"),u=Symbol.for("react.context"),s=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}function v(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}y.prototype.isReactComponent={},y.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},y.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=y.prototype;var w=b.prototype=new v;w.constructor=b,m(w,y.prototype),w.isPureReactComponent=!0;var k=Array.isArray,S=Object.prototype.hasOwnProperty,E={current:null},x={key:!0,ref:!0,__self:!0,__source:!0};function C(e,t,r){var a,o={},l=null,i=null;if(null!=t)for(a in void 0!==t.ref&&(i=t.ref),void 0!==t.key&&(l=""+t.key),t)S.call(t,a)&&!x.hasOwnProperty(a)&&(o[a]=t[a]);var u=arguments.length-2;if(1===u)o.children=r;else if(1<u){for(var s=Array(u),c=0;c<u;c++)s[c]=arguments[c+2];o.children=s}if(e&&e.defaultProps)for(a in u=e.defaultProps)void 0===o[a]&&(o[a]=u[a]);return{$$typeof:n,type:e,key:l,ref:i,props:o,_owner:E.current}}function N(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var R=/\/+/g;function _(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function T(e,t,a,o,l){var i=typeof e;"undefined"!==i&&"boolean"!==i||(e=null);var u=!1;if(null===e)u=!0;else switch(i){case"string":case"number":u=!0;break;case"object":switch(e.$$typeof){case n:case r:u=!0}}if(u)return l=l(u=e),e=""===o?"."+_(u,0):o,k(l)?(a="",null!=e&&(a=e.replace(R,"$&/")+"/"),T(l,t,a,"",(function(e){return e}))):null!=l&&(N(l)&&(l=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(l,a+(!l.key||u&&u.key===l.key?"":(""+l.key).replace(R,"$&/")+"/")+e)),t.push(l)),1;if(u=0,o=""===o?".":o+":",k(e))for(var s=0;s<e.length;s++){var c=o+_(i=e[s],s);u+=T(i,t,a,c,l)}else if(c=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=p&&e[p]||e["@@iterator"])?e:null}(e),"function"===typeof c)for(e=c.call(e),s=0;!(i=e.next()).done;)u+=T(i=i.value,t,a,c=o+_(i,s++),l);else if("object"===i)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return u}function P(e,t,n){if(null==e)return e;var r=[],a=0;return T(e,r,"","",(function(e){return t.call(n,e,a++)})),r}function O(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var L={current:null},j={transition:null},z={ReactCurrentDispatcher:L,ReactCurrentBatchConfig:j,ReactCurrentOwner:E};function F(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:P,forEach:function(e,t,n){P(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return P(e,(function(){t++})),t},toArray:function(e){return P(e,(function(e){return e}))||[]},only:function(e){if(!N(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=y,t.Fragment=a,t.Profiler=l,t.PureComponent=b,t.StrictMode=o,t.Suspense=c,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=z,t.act=F,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=m({},e.props),o=e.key,l=e.ref,i=e._owner;if(null!=t){if(void 0!==t.ref&&(l=t.ref,i=E.current),void 0!==t.key&&(o=""+t.key),e.type&&e.type.defaultProps)var u=e.type.defaultProps;for(s in t)S.call(t,s)&&!x.hasOwnProperty(s)&&(a[s]=void 0===t[s]&&void 0!==u?u[s]:t[s])}var s=arguments.length-2;if(1===s)a.children=r;else if(1<s){u=Array(s);for(var c=0;c<s;c++)u[c]=arguments[c+2];a.children=u}return{$$typeof:n,type:e.type,key:o,ref:l,props:a,_owner:i}},t.createContext=function(e){return(e={$$typeof:u,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:i,_context:e},e.Consumer=e},t.createElement=C,t.createFactory=function(e){var t=C.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:s,render:e}},t.isValidElement=N,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:O}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=j.transition;j.transition={};try{e()}finally{j.transition=t}},t.unstable_act=F,t.useCallback=function(e,t){return L.current.useCallback(e,t)},t.useContext=function(e){return L.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return L.current.useDeferredValue(e)},t.useEffect=function(e,t){return L.current.useEffect(e,t)},t.useId=function(){return L.current.useId()},t.useImperativeHandle=function(e,t,n){return L.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return L.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return L.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return L.current.useMemo(e,t)},t.useReducer=function(e,t,n){return L.current.useReducer(e,t,n)},t.useRef=function(e){return L.current.useRef(e)},t.useState=function(e){return L.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return L.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return L.current.useTransition()},t.version="18.3.1"},43:(e,t,n)=>{e.exports=n(202)},579:(e,t,n)=>{e.exports=n(153)},234:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<o(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,l=a>>>1;r<l;){var i=2*(r+1)-1,u=e[i],s=i+1,c=e[s];if(0>o(u,n))s<a&&0>o(c,u)?(e[r]=c,e[s]=n,r=s):(e[r]=u,e[i]=n,r=i);else{if(!(s<a&&0>o(c,n)))break e;e[r]=c,e[s]=n,r=s}}}return t}function o(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var l=performance;t.unstable_now=function(){return l.now()}}else{var i=Date,u=i.now();t.unstable_now=function(){return i.now()-u}}var s=[],c=[],d=1,f=null,p=3,h=!1,m=!1,g=!1,y="function"===typeof setTimeout?setTimeout:null,v="function"===typeof clearTimeout?clearTimeout:null,b="undefined"!==typeof setImmediate?setImmediate:null;function w(e){for(var t=r(c);null!==t;){if(null===t.callback)a(c);else{if(!(t.startTime<=e))break;a(c),t.sortIndex=t.expirationTime,n(s,t)}t=r(c)}}function k(e){if(g=!1,w(e),!m)if(null!==r(s))m=!0,j(S);else{var t=r(c);null!==t&&z(k,t.startTime-e)}}function S(e,n){m=!1,g&&(g=!1,v(N),N=-1),h=!0;var o=p;try{for(w(n),f=r(s);null!==f&&(!(f.expirationTime>n)||e&&!T());){var l=f.callback;if("function"===typeof l){f.callback=null,p=f.priorityLevel;var i=l(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof i?f.callback=i:f===r(s)&&a(s),w(n)}else a(s);f=r(s)}if(null!==f)var u=!0;else{var d=r(c);null!==d&&z(k,d.startTime-n),u=!1}return u}finally{f=null,p=o,h=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var E,x=!1,C=null,N=-1,R=5,_=-1;function T(){return!(t.unstable_now()-_<R)}function P(){if(null!==C){var e=t.unstable_now();_=e;var n=!0;try{n=C(!0,e)}finally{n?E():(x=!1,C=null)}}else x=!1}if("function"===typeof b)E=function(){b(P)};else if("undefined"!==typeof MessageChannel){var O=new MessageChannel,L=O.port2;O.port1.onmessage=P,E=function(){L.postMessage(null)}}else E=function(){y(P,0)};function j(e){C=e,x||(x=!0,E())}function z(e,n){N=y((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){m||h||(m=!0,j(S))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):R=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return r(s)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,a,o){var l=t.unstable_now();switch("object"===typeof o&&null!==o?o="number"===typeof(o=o.delay)&&0<o?l+o:l:o=l,e){case 1:var i=-1;break;case 2:i=250;break;case 5:i=1073741823;break;case 4:i=1e4;break;default:i=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:o,expirationTime:i=o+i,sortIndex:-1},o>l?(e.sortIndex=o,n(c,e),null===r(s)&&e===r(c)&&(g?(v(N),N=-1):g=!0,z(k,o-l))):(e.sortIndex=i,n(s,e),m||h||(m=!0,j(S))),e},t.unstable_shouldYield=T,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},853:(e,t,n)=>{e.exports=n(234)}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}n.m=e,n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce(((t,r)=>(n.f[r](e,t),t)),[])),n.u=e=>"static/js/"+e+".64034758.chunk.js",n.miniCssF=e=>{},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="shopify-react-extension:";n.l=(r,a,o,l)=>{if(e[r])e[r].push(a);else{var i,u;if(void 0!==o)for(var s=document.getElementsByTagName("script"),c=0;c<s.length;c++){var d=s[c];if(d.getAttribute("src")==r||d.getAttribute("data-webpack")==t+o){i=d;break}}i||(u=!0,(i=document.createElement("script")).charset="utf-8",i.timeout=120,n.nc&&i.setAttribute("nonce",n.nc),i.setAttribute("data-webpack",t+o),i.src=r),e[r]=[a];var f=(t,n)=>{i.onerror=i.onload=null,clearTimeout(p);var a=e[r];if(delete e[r],i.parentNode&&i.parentNode.removeChild(i),a&&a.forEach((e=>e(n))),t)return t(n)},p=setTimeout(f.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=f.bind(null,i.onerror),i.onload=f.bind(null,i.onload),u&&document.head.appendChild(i)}}})(),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.p="/",(()=>{var e={792:0};n.f.j=(t,r)=>{var a=n.o(e,t)?e[t]:void 0;if(0!==a)if(a)r.push(a[2]);else{var o=new Promise(((n,r)=>a=e[t]=[n,r]));r.push(a[2]=o);var l=n.p+n.u(t),i=new Error;n.l(l,(r=>{if(n.o(e,t)&&(0!==(a=e[t])&&(e[t]=void 0),a)){var o=r&&("load"===r.type?"missing":r.type),l=r&&r.target&&r.target.src;i.message="Loading chunk "+t+" failed.\n("+o+": "+l+")",i.name="ChunkLoadError",i.type=o,i.request=l,a[1](i)}}),"chunk-"+t,t)}};var t=(t,r)=>{var a,o,l=r[0],i=r[1],u=r[2],s=0;if(l.some((t=>0!==e[t]))){for(a in i)n.o(i,a)&&(n.m[a]=i[a]);if(u)u(n)}for(t&&t(r);s<l.length;s++)o=l[s],n.o(e,o)&&e[o]&&e[o][0](),e[o]=0},r=self.webpackChunkshopify_react_extension=self.webpackChunkshopify_react_extension||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))})();var r={};n.r(r),n.d(r,{hasBrowserEnv:()=>yn,hasStandardBrowserEnv:()=>bn,hasStandardBrowserWebWorkerEnv:()=>wn,navigator:()=>vn,origin:()=>kn});var a=n(43),o=n(391),l=(n(358),"popstate");function i(){return h((function(e,t){let{pathname:n="/",search:r="",hash:a=""}=p(e.location.hash.substring(1));return n.startsWith("/")||n.startsWith(".")||(n="/"+n),d("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){let n=e.document.querySelector("base"),r="";if(n&&n.getAttribute("href")){let t=e.location.href,n=t.indexOf("#");r=-1===n?t:t.slice(0,n)}return r+"#"+("string"===typeof t?t:f(t))}),(function(e,t){s("/"===e.pathname.charAt(0),`relative pathnames are not supported in hash history.push(${JSON.stringify(t)})`)}),arguments.length>0&&void 0!==arguments[0]?arguments[0]:{})}function u(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function s(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function c(e,t){return{usr:e.state,key:e.key,idx:t}}function d(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,r=arguments.length>3?arguments[3]:void 0;return{pathname:"string"===typeof e?e:e.pathname,search:"",hash:"",..."string"===typeof t?p(t):t,state:n,key:t&&t.key||r||Math.random().toString(36).substring(2,10)}}function f(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function p(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function h(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},{window:a=document.defaultView,v5Compat:o=!1}=r,i=a.history,s="POP",p=null,h=m();function m(){return(i.state||{idx:null}).idx}function g(){s="POP";let e=m(),t=null==e?null:e-h;h=e,p&&p({action:s,location:v.location,delta:t})}function y(e){let t="null"!==a.location.origin?a.location.origin:a.location.href,n="string"===typeof e?e:f(e);return n=n.replace(/ $/,"%20"),u(t,`No window.location.(origin|href) available to create URL for href: ${n}`),new URL(n,t)}null==h&&(h=0,i.replaceState({...i.state,idx:h},""));let v={get action(){return s},get location(){return e(a,i)},listen(e){if(p)throw new Error("A history only accepts one active listener");return a.addEventListener(l,g),p=e,()=>{a.removeEventListener(l,g),p=null}},createHref:e=>t(a,e),createURL:y,encodeLocation(e){let t=y(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){s="PUSH";let r=d(v.location,e,t);n&&n(r,e),h=m()+1;let l=c(r,h),u=v.createHref(r);try{i.pushState(l,"",u)}catch(f){if(f instanceof DOMException&&"DataCloneError"===f.name)throw f;a.location.assign(u)}o&&p&&p({action:s,location:v.location,delta:1})},replace:function(e,t){s="REPLACE";let r=d(v.location,e,t);n&&n(r,e),h=m();let a=c(r,h),l=v.createHref(r);i.replaceState(a,"",l),o&&p&&p({action:s,location:v.location,delta:0})},go:e=>i.go(e)};return v}new WeakMap;function m(e,t){return g(e,t,arguments.length>2&&void 0!==arguments[2]?arguments[2]:"/",!1)}function g(e,t,n,r){let a=P(("string"===typeof t?p(t):t).pathname||"/",n);if(null==a)return null;let o=y(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every(((e,n)=>e===t[n]));return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(o);let l=null;for(let i=0;null==l&&i<o.length;++i){let e=T(a);l=R(o[i],e,r)}return l}function y(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"",a=(e,a,o)=>{let l={relativePath:void 0===o?e.path||"":o,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};l.relativePath.startsWith("/")&&(u(l.relativePath.startsWith(r),`Absolute route path "${l.relativePath}" nested under path "${r}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),l.relativePath=l.relativePath.slice(r.length));let i=F([r,l.relativePath]),s=n.concat(l);e.children&&e.children.length>0&&(u(!0!==e.index,`Index routes must not have child routes. Please remove all child routes from route path "${i}".`),y(e.children,t,s,i)),(null!=e.path||e.index)&&t.push({path:i,score:N(i,e.index),routesMeta:s})};return e.forEach(((e,t)=>{if(""!==e.path&&e.path?.includes("?"))for(let n of v(e.path))a(e,t,n);else a(e,t)})),t}function v(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),o=n.replace(/\?$/,"");if(0===r.length)return a?[o,""]:[o];let l=v(r.join("/")),i=[];return i.push(...l.map((e=>""===e?o:[o,e].join("/")))),a&&i.push(...l),i.map((t=>e.startsWith("/")&&""===t?"/":t))}var b=/^:[\w-]+$/,w=3,k=2,S=1,E=10,x=-2,C=e=>"*"===e;function N(e,t){let n=e.split("/"),r=n.length;return n.some(C)&&(r+=x),t&&(r+=k),n.filter((e=>!C(e))).reduce(((e,t)=>e+(b.test(t)?w:""===t?S:E)),r)}function R(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],{routesMeta:r}=e,a={},o="/",l=[];for(let i=0;i<r.length;++i){let e=r[i],u=i===r.length-1,s="/"===o?t:t.slice(o.length)||"/",c=_({path:e.relativePath,caseSensitive:e.caseSensitive,end:u},s),d=e.route;if(!c&&u&&n&&!r[r.length-1].route.index&&(c=_({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},s)),!c)return null;Object.assign(a,c.params),l.push({params:a,pathname:F([o,c.pathname]),pathnameBase:A(F([o,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(o=F([o,c.pathnameBase]))}return l}function _(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];s("*"===e||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))");let o=new RegExp(a,t?void 0:"i");return[o,r]}(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let o=a[0],l=o.replace(/(.)\/+$/,"$1"),i=a.slice(1);return{params:r.reduce(((e,t,n)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=i[n]||"";l=o.slice(0,o.length-e.length).replace(/(.)\/+$/,"$1")}const u=i[n];return e[r]=a&&!u?void 0:(u||"").replace(/%2F/g,"/"),e}),{}),pathname:o,pathnameBase:l,pattern:e}}function T(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return s(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function P(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function O(e,t,n,r){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(r)}].  Please separate it out to the \`to.${n}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function L(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}function j(e){let t=L(e);return t.map(((e,n)=>n===t.length-1?e.pathname:e.pathnameBase))}function z(e,t,n){let r,a=arguments.length>3&&void 0!==arguments[3]&&arguments[3];"string"===typeof e?r=p(e):(r={...e},u(!r.pathname||!r.pathname.includes("?"),O("?","pathname","search",r)),u(!r.pathname||!r.pathname.includes("#"),O("#","pathname","hash",r)),u(!r.search||!r.search.includes("#"),O("#","search","hash",r)));let o,l=""===e||""===r.pathname,i=l?"/":r.pathname;if(null==i)o=n;else{let e=t.length-1;if(!a&&i.startsWith("..")){let t=i.split("/");for(;".."===t[0];)t.shift(),e-=1;r.pathname=t.join("/")}o=e>=0?t[e]:"/"}let s=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"/",{pathname:n,search:r="",hash:a=""}="string"===typeof e?p(e):e,o=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:o,search:M(r),hash:D(a)}}(r,o),c=i&&"/"!==i&&i.endsWith("/"),d=(l||"."===i)&&n.endsWith("/");return s.pathname.endsWith("/")||!c&&!d||(s.pathname+="/"),s}var F=e=>e.join("/").replace(/\/\/+/g,"/"),A=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),M=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",D=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";function U(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}var I=["POST","PUT","PATCH","DELETE"],$=(new Set(I),["GET",...I]);new Set($),Symbol("ResetLoaderData");var B=a.createContext(null);B.displayName="DataRouter";var W=a.createContext(null);W.displayName="DataRouterState";var H=a.createContext({isTransitioning:!1});H.displayName="ViewTransition";var V=a.createContext(new Map);V.displayName="Fetchers";var q=a.createContext(null);q.displayName="Await";var Q=a.createContext(null);Q.displayName="Navigation";var K=a.createContext(null);K.displayName="Location";var J=a.createContext({outlet:null,matches:[],isDataRoute:!1});J.displayName="Route";var Y=a.createContext(null);Y.displayName="RouteError";function X(){return null!=a.useContext(K)}function G(){return u(X(),"useLocation() may be used only in the context of a <Router> component."),a.useContext(K).location}var Z="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function ee(e){a.useContext(Q).static||a.useLayoutEffect(e)}function te(){let{isDataRoute:e}=a.useContext(J);return e?function(){let{router:e}=ce("useNavigate"),t=fe("useNavigate"),n=a.useRef(!1);ee((()=>{n.current=!0}));let r=a.useCallback((async function(r){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};s(n.current,Z),n.current&&("number"===typeof r?e.navigate(r):await e.navigate(r,{fromRouteId:t,...a}))}),[e,t]);return r}():function(){u(X(),"useNavigate() may be used only in the context of a <Router> component.");let e=a.useContext(B),{basename:t,navigator:n}=a.useContext(Q),{matches:r}=a.useContext(J),{pathname:o}=G(),l=JSON.stringify(j(r)),i=a.useRef(!1);ee((()=>{i.current=!0}));let c=a.useCallback((function(r){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(s(i.current,Z),!i.current)return;if("number"===typeof r)return void n.go(r);let u=z(r,JSON.parse(l),o,"path"===a.relative);null==e&&"/"!==t&&(u.pathname="/"===u.pathname?t:F([t,u.pathname])),(a.replace?n.replace:n.push)(u,a.state,a)}),[t,n,l,o,e]);return c}()}a.createContext(null);function ne(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{matches:n}=a.useContext(J),{pathname:r}=G(),o=JSON.stringify(j(n));return a.useMemo((()=>z(e,JSON.parse(o),r,"path"===t)),[e,o,r,t])}function re(e,t,n,r){u(X(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:o,static:l}=a.useContext(Q),{matches:i}=a.useContext(J),c=i[i.length-1],d=c?c.params:{},f=c?c.pathname:"/",h=c?c.pathnameBase:"/",g=c&&c.route;{let e=g&&g.path||"";me(f,!g||e.endsWith("*")||e.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${f}" (under <Route path="${e}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.\n\nPlease change the parent <Route path="${e}"> to <Route path="${"/"===e?"*":`${e}/*`}">.`)}let y,v=G();if(t){let e="string"===typeof t?p(t):t;u("/"===h||e.pathname?.startsWith(h),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${h}" but pathname "${e.pathname}" was given in the \`location\` prop.`),y=e}else y=v;let b=y.pathname||"/",w=b;if("/"!==h){let e=h.replace(/^\//,"").split("/");w="/"+b.replace(/^\//,"").split("/").slice(e.length).join("/")}let k=!l&&n&&n.matches&&n.matches.length>0?n.matches:m(e,{pathname:w});s(g||null!=k,`No routes matched location "${y.pathname}${y.search}${y.hash}" `),s(null==k||void 0!==k[k.length-1].route.element||void 0!==k[k.length-1].route.Component||void 0!==k[k.length-1].route.lazy,`Matched leaf route at location "${y.pathname}${y.search}${y.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let S=ue(k&&k.map((e=>Object.assign({},e,{params:Object.assign({},d,e.params),pathname:F([h,o.encodeLocation?o.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?h:F([h,o.encodeLocation?o.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),i,n,r);return t&&S?a.createElement(K.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...y},navigationType:"POP"}},S):S}function ae(){let e=pe(),t=U(e)?`${e.status} ${e.statusText}`:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,r="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:r},l={padding:"2px 4px",backgroundColor:r},i=null;return console.error("Error handled by React Router default ErrorBoundary:",e),i=a.createElement(a.Fragment,null,a.createElement("p",null,"\ud83d\udcbf Hey developer \ud83d\udc4b"),a.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",a.createElement("code",{style:l},"ErrorBoundary")," or"," ",a.createElement("code",{style:l},"errorElement")," prop on your route.")),a.createElement(a.Fragment,null,a.createElement("h2",null,"Unexpected Application Error!"),a.createElement("h3",{style:{fontStyle:"italic"}},t),n?a.createElement("pre",{style:o},n):null,i)}var oe=a.createElement(ae,null),le=class extends a.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?a.createElement(J.Provider,{value:this.props.routeContext},a.createElement(Y.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function ie(e){let{routeContext:t,match:n,children:r}=e,o=a.useContext(B);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),a.createElement(J.Provider,{value:t},r)}function ue(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(null==e){if(!n)return null;if(n.errors)e=n.matches;else{if(0!==t.length||n.initialized||!(n.matches.length>0))return null;e=n.matches}}let r=e,o=n?.errors;if(null!=o){let e=r.findIndex((e=>e.route.id&&void 0!==o?.[e.route.id]));u(e>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(o).join(",")}`),r=r.slice(0,Math.min(r.length,e+1))}let l=!1,i=-1;if(n)for(let a=0;a<r.length;a++){let e=r[a];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(i=a),e.route.id){let{loaderData:t,errors:a}=n,o=e.route.loader&&!t.hasOwnProperty(e.route.id)&&(!a||void 0===a[e.route.id]);if(e.route.lazy||o){l=!0,r=i>=0?r.slice(0,i+1):[r[0]];break}}}return r.reduceRight(((e,u,s)=>{let c,d=!1,f=null,p=null;n&&(c=o&&u.route.id?o[u.route.id]:void 0,f=u.route.errorElement||oe,l&&(i<0&&0===s?(me("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),d=!0,p=null):i===s&&(d=!0,p=u.route.hydrateFallbackElement||null)));let h=t.concat(r.slice(0,s+1)),m=()=>{let t;return t=c?f:d?p:u.route.Component?a.createElement(u.route.Component,null):u.route.element?u.route.element:e,a.createElement(ie,{match:u,routeContext:{outlet:e,matches:h,isDataRoute:null!=n},children:t})};return n&&(u.route.ErrorBoundary||u.route.errorElement||0===s)?a.createElement(le,{location:n.location,revalidation:n.revalidation,component:f,error:c,children:m(),routeContext:{outlet:null,matches:h,isDataRoute:!0}}):m()}),null)}function se(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function ce(e){let t=a.useContext(B);return u(t,se(e)),t}function de(e){let t=a.useContext(W);return u(t,se(e)),t}function fe(e){let t=function(e){let t=a.useContext(J);return u(t,se(e)),t}(e),n=t.matches[t.matches.length-1];return u(n.route.id,`${e} can only be used on routes that contain a unique "id"`),n.route.id}function pe(){let e=a.useContext(Y),t=de("useRouteError"),n=fe("useRouteError");return void 0!==e?e:t.errors?.[n]}var he={};function me(e,t,n){t||he[e]||(he[e]=!0,s(!1,n))}a.memo((function(e){let{routes:t,future:n,state:r}=e;return re(t,void 0,r,n)}));function ge(e){let{to:t,replace:n,state:r,relative:o}=e;u(X(),"<Navigate> may be used only in the context of a <Router> component.");let{static:l}=a.useContext(Q);s(!l,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:i}=a.useContext(J),{pathname:c}=G(),d=te(),f=z(t,j(i),c,"path"===o),p=JSON.stringify(f);return a.useEffect((()=>{d(JSON.parse(p),{replace:n,state:r,relative:o})}),[d,p,o,n,r]),null}function ye(e){u(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function ve(e){let{basename:t="/",children:n=null,location:r,navigationType:o="POP",navigator:l,static:i=!1}=e;u(!X(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let c=t.replace(/^\/*/,"/"),d=a.useMemo((()=>({basename:c,navigator:l,static:i,future:{}})),[c,l,i]);"string"===typeof r&&(r=p(r));let{pathname:f="/",search:h="",hash:m="",state:g=null,key:y="default"}=r,v=a.useMemo((()=>{let e=P(f,c);return null==e?null:{location:{pathname:e,search:h,hash:m,state:g,key:y},navigationType:o}}),[c,f,h,m,g,y,o]);return s(null!=v,`<Router basename="${c}"> is not able to match the URL "${f}${h}${m}" because it does not start with the basename, so the <Router> won't render anything.`),null==v?null:a.createElement(Q.Provider,{value:d},a.createElement(K.Provider,{children:n,value:v}))}function be(e){let{children:t,location:n}=e;return re(we(t),n)}a.Component;function we(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=[];return a.Children.forEach(e,((e,r)=>{if(!a.isValidElement(e))return;let o=[...t,r];if(e.type===a.Fragment)return void n.push.apply(n,we(e.props.children,o));u(e.type===ye,`[${"string"===typeof e.type?e.type:e.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),u(!e.props.index||!e.props.children,"An index route cannot have child routes.");let l={id:e.props.id||o.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,hydrateFallbackElement:e.props.hydrateFallbackElement,HydrateFallback:e.props.HydrateFallback,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:!0===e.props.hasErrorBoundary||null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(l.children=we(e.props.children,o)),n.push(l)})),n}var ke="get",Se="application/x-www-form-urlencoded";function Ee(e){return null!=e&&"string"===typeof e.tagName}var xe=null;var Ce=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function Ne(e){return null==e||Ce.has(e)?e:(s(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${Se}"`),null)}function Re(e,t){let n,r,a,o,l;if(Ee(i=e)&&"form"===i.tagName.toLowerCase()){let l=e.getAttribute("action");r=l?P(l,t):null,n=e.getAttribute("method")||ke,a=Ne(e.getAttribute("enctype"))||Se,o=new FormData(e)}else if(function(e){return Ee(e)&&"button"===e.tagName.toLowerCase()}(e)||function(e){return Ee(e)&&"input"===e.tagName.toLowerCase()}(e)&&("submit"===e.type||"image"===e.type)){let l=e.form;if(null==l)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let i=e.getAttribute("formaction")||l.getAttribute("action");if(r=i?P(i,t):null,n=e.getAttribute("formmethod")||l.getAttribute("method")||ke,a=Ne(e.getAttribute("formenctype"))||Ne(l.getAttribute("enctype"))||Se,o=new FormData(l,e),!function(){if(null===xe)try{new FormData(document.createElement("form"),0),xe=!1}catch(e){xe=!0}return xe}()){let{name:t,type:n,value:r}=e;if("image"===n){let e=t?`${t}.`:"";o.append(`${e}x`,"0"),o.append(`${e}y`,"0")}else t&&o.append(t,r)}}else{if(Ee(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=ke,r=null,a=Se,l=e}var i;return o&&"text/plain"===a&&(l=o,o=void 0),{action:r,method:n.toLowerCase(),encType:a,formData:o,body:l}}function _e(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}async function Te(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(n){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(n),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise((()=>{}))}}function Pe(e){return null!=e&&"string"===typeof e.page}function Oe(e){return null!=e&&(null==e.href?"preload"===e.rel&&"string"===typeof e.imageSrcSet&&"string"===typeof e.imageSizes:"string"===typeof e.rel&&"string"===typeof e.href)}function Le(e,t,n,r,a,o){let l=(e,t)=>!n[t]||e.route.id!==n[t].route.id,i=(e,t)=>n[t].pathname!==e.pathname||n[t].route.path?.endsWith("*")&&n[t].params["*"]!==e.params["*"];return"assets"===o?t.filter(((e,t)=>l(e,t)||i(e,t))):"data"===o?t.filter(((t,o)=>{let u=r.routes[t.route.id];if(!u||!u.hasLoader)return!1;if(l(t,o)||i(t,o))return!0;if(t.route.shouldRevalidate){let r=t.route.shouldRevalidate({currentUrl:new URL(a.pathname+a.search+a.hash,window.origin),currentParams:n[0]?.params||{},nextUrl:new URL(e,window.origin),nextParams:t.params,defaultShouldRevalidate:!0});if("boolean"===typeof r)return r}return!0})):[]}function je(e,t){let{includeHydrateFallback:n}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return r=e.map((e=>{let r=t.routes[e.route.id];if(!r)return[];let a=[r.module];return r.clientActionModule&&(a=a.concat(r.clientActionModule)),r.clientLoaderModule&&(a=a.concat(r.clientLoaderModule)),n&&r.hydrateFallbackModule&&(a=a.concat(r.hydrateFallbackModule)),r.imports&&(a=a.concat(r.imports)),a})).flat(1),[...new Set(r)];var r}function ze(e,t){let n=new Set,r=new Set(t);return e.reduce(((e,a)=>{if(t&&!Pe(a)&&"script"===a.as&&a.href&&r.has(a.href))return e;let o=JSON.stringify(function(e){let t={},n=Object.keys(e).sort();for(let r of n)t[r]=e[r];return t}(a));return n.has(o)||(n.add(o),e.push({key:o,link:a})),e}),[])}function Fe(e){return{__html:e}}Symbol("SingleFetchRedirect");function Ae(e,t){let n="string"===typeof e?new URL(e,"undefined"===typeof window?"server://singlefetch/":window.location.origin):e;return"/"===n.pathname?n.pathname="_root.data":t&&"/"===P(n.pathname,t)?n.pathname=`${t.replace(/\/$/,"")}/_root.data`:n.pathname=`${n.pathname.replace(/\/$/,"")}.data`,n}a.Component;function Me(e){let{error:t,isOutsideRemixApp:n}=e;console.error(t);let r,o=a.createElement("script",{dangerouslySetInnerHTML:{__html:'\n        console.log(\n          "\ud83d\udcbf Hey developer \ud83d\udc4b. You can provide a way better UX than this when your app throws errors. Check out https://remix.run/guides/errors for more information."\n        );\n      '}});if(U(t))return a.createElement(De,{title:"Unhandled Thrown Response!"},a.createElement("h1",{style:{fontSize:"24px"}},t.status," ",t.statusText),o);if(t instanceof Error)r=t;else{let e=null==t?"Unknown Error":"object"===typeof t&&"toString"in t?t.toString():JSON.stringify(t);r=new Error(e)}return a.createElement(De,{title:"Application Error!",isOutsideRemixApp:n},a.createElement("h1",{style:{fontSize:"24px"}},"Application Error"),a.createElement("pre",{style:{padding:"2rem",background:"hsla(10, 50%, 50%, 0.1)",color:"red",overflow:"auto"}},r.stack),o)}function De(e){let{title:t,renderScripts:n,isOutsideRemixApp:r,children:o}=e,{routeModules:l}=We();return l.root?.Layout&&!r?o:a.createElement("html",{lang:"en"},a.createElement("head",null,a.createElement("meta",{charSet:"utf-8"}),a.createElement("meta",{name:"viewport",content:"width=device-width,initial-scale=1,viewport-fit=cover"}),a.createElement("title",null,t)),a.createElement("body",null,a.createElement("main",{style:{fontFamily:"system-ui, sans-serif",padding:"2rem"}},o,n?a.createElement(Ye,null):null)))}function Ue(e){return!0===e}function Ie(){let e=a.useContext(B);return _e(e,"You must render this element inside a <DataRouterContext.Provider> element"),e}function $e(){let e=a.useContext(W);return _e(e,"You must render this element inside a <DataRouterStateContext.Provider> element"),e}var Be=a.createContext(void 0);function We(){let e=a.useContext(Be);return _e(e,"You must render this element inside a <HydratedRouter> element"),e}function He(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function Ve(e,t,n){if(n&&!Je)return[e[0]];if(t){let n=e.findIndex((e=>void 0!==t[e.route.id]));return e.slice(0,n+1)}return e}function qe(e){let{page:t,...n}=e,{router:r}=Ie(),o=a.useMemo((()=>m(r.routes,t,r.basename)),[r.routes,t,r.basename]);return o?a.createElement(Ke,{page:t,matches:o,...n}):null}function Qe(e){let{manifest:t,routeModules:n}=We(),[r,o]=a.useState([]);return a.useEffect((()=>{let r=!1;return async function(e,t,n){return ze((await Promise.all(e.map((async e=>{let r=t.routes[e.route.id];if(r){let e=await Te(r,n);return e.links?e.links():[]}return[]})))).flat(1).filter(Oe).filter((e=>"stylesheet"===e.rel||"preload"===e.rel)).map((e=>"stylesheet"===e.rel?{...e,rel:"prefetch",as:"style"}:{...e,rel:"prefetch"})))}(e,t,n).then((e=>{r||o(e)})),()=>{r=!0}}),[e,t,n]),r}function Ke(e){let{page:t,matches:n,...r}=e,o=G(),{manifest:l,routeModules:i}=We(),{basename:u}=Ie(),{loaderData:s,matches:c}=$e(),d=a.useMemo((()=>Le(t,n,c,l,o,"data")),[t,n,c,l,o]),f=a.useMemo((()=>Le(t,n,c,l,o,"assets")),[t,n,c,l,o]),p=a.useMemo((()=>{if(t===o.pathname+o.search+o.hash)return[];let e=new Set,r=!1;if(n.forEach((t=>{let n=l.routes[t.route.id];n&&n.hasLoader&&(!d.some((e=>e.route.id===t.route.id))&&t.route.id in s&&i[t.route.id]?.shouldRevalidate||n.hasClientLoader?r=!0:e.add(t.route.id))})),0===e.size)return[];let a=Ae(t,u);return r&&e.size>0&&a.searchParams.set("_routes",n.filter((t=>e.has(t.route.id))).map((e=>e.route.id)).join(",")),[a.pathname+a.search]}),[u,s,o,l,d,n,t,i]),h=a.useMemo((()=>je(f,l)),[f,l]),m=Qe(f);return a.createElement(a.Fragment,null,p.map((e=>a.createElement("link",{key:e,rel:"prefetch",as:"fetch",href:e,...r}))),h.map((e=>a.createElement("link",{key:e,rel:"modulepreload",href:e,...r}))),m.map((e=>{let{key:t,link:n}=e;return a.createElement("link",{key:t,...n})})))}Be.displayName="FrameworkContext";var Je=!1;function Ye(e){let{manifest:t,serverHandoffString:n,isSpaMode:r,ssr:o,renderMeta:l}=We(),{router:i,static:u,staticContext:s}=Ie(),{matches:c}=$e(),d=Ue(o);l&&(l.didRenderScripts=!0);let f=Ve(c,null,r);a.useEffect((()=>{Je=!0}),[]);let p=a.useMemo((()=>{let r=s?`window.__reactRouterContext = ${n};window.__reactRouterContext.stream = new ReadableStream({start(controller){window.__reactRouterContext.streamController = controller;}}).pipeThrough(new TextEncoderStream());`:" ",o=u?`${t.hmr?.runtime?`import ${JSON.stringify(t.hmr.runtime)};`:""}${d?"":`import ${JSON.stringify(t.url)}`};\n${f.map(((e,n)=>{let r=`route${n}`,a=t.routes[e.route.id];_e(a,`Route ${e.route.id} not found in manifest`);let{clientActionModule:o,clientLoaderModule:l,hydrateFallbackModule:i,module:u}=a,s=[...o?[{module:o,varName:`${r}_clientAction`}]:[],...l?[{module:l,varName:`${r}_clientLoader`}]:[],...i?[{module:i,varName:`${r}_HydrateFallback`}]:[],{module:u,varName:`${r}_main`}];return 1===s.length?`import * as ${r} from ${JSON.stringify(u)};`:[s.map((e=>`import * as ${e.varName} from "${e.module}";`)).join("\n"),`const ${r} = {${s.map((e=>`...${e.varName}`)).join(",")}};`].join("\n")})).join("\n")}\n  ${d?`window.__reactRouterManifest = ${JSON.stringify(function(e,t){let n=new Set(t.state.matches.map((e=>e.route.id))),r=t.state.location.pathname.split("/").filter(Boolean),a=["/"];for(r.pop();r.length>0;)a.push(`/${r.join("/")}`),r.pop();a.forEach((e=>{let r=m(t.routes,e,t.basename);r&&r.forEach((e=>n.add(e.route.id)))}));let o=[...n].reduce(((t,n)=>Object.assign(t,{[n]:e.routes[n]})),{});return{...e,routes:o}}(t,i),null,2)};`:""}\n  window.__reactRouterRouteModules = {${f.map(((e,t)=>`${JSON.stringify(e.route.id)}:route${t}`)).join(",")}};\n\nimport(${JSON.stringify(t.entry.module)});`:" ";return a.createElement(a.Fragment,null,a.createElement("script",{...e,suppressHydrationWarning:!0,dangerouslySetInnerHTML:Fe(r),type:void 0}),a.createElement("script",{...e,suppressHydrationWarning:!0,dangerouslySetInnerHTML:Fe(o),type:"module",async:!0}))}),[]),h=Je?[]:t.entry.imports.concat(je(f,t,{includeHydrateFallback:!0}));return Je?null:a.createElement(a.Fragment,null,d?null:a.createElement("link",{rel:"modulepreload",href:t.url,crossOrigin:e.crossOrigin}),a.createElement("link",{rel:"modulepreload",href:t.entry.module,crossOrigin:e.crossOrigin}),(g=h,[...new Set(g)]).map((t=>a.createElement("link",{key:t,rel:"modulepreload",href:t,crossOrigin:e.crossOrigin}))),p);var g}function Xe(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return e=>{t.forEach((t=>{"function"===typeof t?t(e):null!=t&&(t.current=e)}))}}var Ge="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement;try{Ge&&(window.__reactRouterVersion="7.4.0")}catch(Jr){}function Ze(e){let{basename:t,children:n,window:r}=e,o=a.useRef();null==o.current&&(o.current=i({window:r,v5Compat:!0}));let l=o.current,[u,s]=a.useState({action:l.action,location:l.location}),c=a.useCallback((e=>{a.startTransition((()=>s(e)))}),[s]);return a.useLayoutEffect((()=>l.listen(c)),[l,c]),a.createElement(ve,{basename:t,children:n,location:u.location,navigationType:u.action,navigator:l})}var et=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,tt=a.forwardRef((function(e,t){let n,{onClick:r,discover:o="render",prefetch:l="none",relative:i,reloadDocument:c,replace:d,state:p,target:h,to:m,preventScrollReset:g,viewTransition:y,...v}=e,{basename:b}=a.useContext(Q),w="string"===typeof m&&et.test(m),k=!1;if("string"===typeof m&&w&&(n=m,Ge))try{let e=new URL(window.location.href),t=m.startsWith("//")?new URL(e.protocol+m):new URL(m),n=P(t.pathname,b);t.origin===e.origin&&null!=n?m=n+t.search+t.hash:k=!0}catch(Jr){s(!1,`<Link to="${m}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let S=function(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};u(X(),"useHref() may be used only in the context of a <Router> component.");let{basename:n,navigator:r}=a.useContext(Q),{hash:o,pathname:l,search:i}=ne(e,{relative:t}),s=l;return"/"!==n&&(s="/"===l?n:F([n,l])),r.createHref({pathname:s,search:i,hash:o})}(m,{relative:i}),[E,x,C]=function(e,t){let n=a.useContext(Be),[r,o]=a.useState(!1),[l,i]=a.useState(!1),{onFocus:u,onBlur:s,onMouseEnter:c,onMouseLeave:d,onTouchStart:f}=t,p=a.useRef(null);a.useEffect((()=>{if("render"===e&&i(!0),"viewport"===e){let e=new IntersectionObserver((e=>{e.forEach((e=>{i(e.isIntersecting)}))}),{threshold:.5});return p.current&&e.observe(p.current),()=>{e.disconnect()}}}),[e]),a.useEffect((()=>{if(r){let e=setTimeout((()=>{i(!0)}),100);return()=>{clearTimeout(e)}}}),[r]);let h=()=>{o(!0)},m=()=>{o(!1),i(!1)};return n?"intent"!==e?[l,p,{}]:[l,p,{onFocus:He(u,h),onBlur:He(s,m),onMouseEnter:He(c,h),onMouseLeave:He(d,m),onTouchStart:He(f,h)}]:[!1,p,{}]}(l,v),N=function(e){let{target:t,replace:n,state:r,preventScrollReset:o,relative:l,viewTransition:i}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},u=te(),s=G(),c=ne(e,{relative:l});return a.useCallback((a=>{if(function(e,t){return 0===e.button&&(!t||"_self"===t)&&!function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)}(a,t)){a.preventDefault();let t=void 0!==n?n:f(s)===f(c);u(e,{replace:t,state:r,preventScrollReset:o,relative:l,viewTransition:i})}}),[s,u,c,n,r,t,e,o,l,i])}(m,{replace:d,state:p,target:h,preventScrollReset:g,relative:i,viewTransition:y});let R=a.createElement("a",{...v,...C,href:n||S,onClick:k||c?r:function(e){r&&r(e),e.defaultPrevented||N(e)},ref:Xe(t,x),target:h,"data-discover":w||"render"!==o?void 0:"true"});return E&&!w?a.createElement(a.Fragment,null,R,a.createElement(qe,{page:S})):R}));tt.displayName="Link",a.forwardRef((function(e,t){let{"aria-current":n="page",caseSensitive:r=!1,className:o="",end:l=!1,style:i,to:s,viewTransition:c,children:d,...f}=e,p=ne(s,{relative:f.relative}),h=G(),m=a.useContext(W),{navigator:g,basename:y}=a.useContext(Q),v=null!=m&&function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=a.useContext(H);u(null!=n,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:r}=at("useViewTransitionState"),o=ne(e,{relative:t.relative});if(!n.isTransitioning)return!1;let l=P(n.currentLocation.pathname,r)||n.currentLocation.pathname,i=P(n.nextLocation.pathname,r)||n.nextLocation.pathname;return null!=_(o.pathname,i)||null!=_(o.pathname,l)}(p)&&!0===c,b=g.encodeLocation?g.encodeLocation(p).pathname:p.pathname,w=h.pathname,k=m&&m.navigation&&m.navigation.location?m.navigation.location.pathname:null;r||(w=w.toLowerCase(),k=k?k.toLowerCase():null,b=b.toLowerCase()),k&&y&&(k=P(k,y)||k);const S="/"!==b&&b.endsWith("/")?b.length-1:b.length;let E,x=w===b||!l&&w.startsWith(b)&&"/"===w.charAt(S),C=null!=k&&(k===b||!l&&k.startsWith(b)&&"/"===k.charAt(b.length)),N={isActive:x,isPending:C,isTransitioning:v},R=x?n:void 0;E="function"===typeof o?o(N):[o,x?"active":null,C?"pending":null,v?"transitioning":null].filter(Boolean).join(" ");let T="function"===typeof i?i(N):i;return a.createElement(tt,{...f,"aria-current":R,className:E,ref:t,style:T,to:s,viewTransition:c},"function"===typeof d?d(N):d)})).displayName="NavLink";var nt=a.forwardRef(((e,t)=>{let{discover:n="render",fetcherKey:r,navigate:o,reloadDocument:l,replace:i,state:s,method:c=ke,action:d,onSubmit:p,relative:h,preventScrollReset:m,viewTransition:g,...y}=e,v=it(),b=function(e){let{relative:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{basename:n}=a.useContext(Q),r=a.useContext(J);u(r,"useFormAction must be used inside a RouteContext");let[o]=r.matches.slice(-1),l={...ne(e||".",{relative:t})},i=G();if(null==e){l.search=i.search;let e=new URLSearchParams(l.search),t=e.getAll("index");if(t.some((e=>""===e))){e.delete("index"),t.filter((e=>e)).forEach((t=>e.append("index",t)));let n=e.toString();l.search=n?`?${n}`:""}}e&&"."!==e||!o.route.index||(l.search=l.search?l.search.replace(/^\?/,"?index&"):"?index");"/"!==n&&(l.pathname="/"===l.pathname?n:F([n,l.pathname]));return f(l)}(d,{relative:h}),w="get"===c.toLowerCase()?"get":"post",k="string"===typeof d&&et.test(d);return a.createElement("form",{ref:t,method:w,action:b,onSubmit:l?p:e=>{if(p&&p(e),e.defaultPrevented)return;e.preventDefault();let t=e.nativeEvent.submitter,n=t?.getAttribute("formmethod")||c;v(t||e.currentTarget,{fetcherKey:r,method:n,navigate:o,replace:i,state:s,relative:h,preventScrollReset:m,viewTransition:g})},...y,"data-discover":k||"render"!==n?void 0:"true"})}));function rt(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function at(e){let t=a.useContext(B);return u(t,rt(e)),t}nt.displayName="Form";var ot=0,lt=()=>`__${String(++ot)}__`;function it(){let{router:e}=at("useSubmit"),{basename:t}=a.useContext(Q),n=fe("useRouteId");return a.useCallback((async function(r){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{action:o,method:l,encType:i,formData:u,body:s}=Re(r,t);if(!1===a.navigate){let t=a.fetcherKey||lt();await e.fetch(t,n,a.action||o,{preventScrollReset:a.preventScrollReset,formData:u,body:s,formMethod:a.method||l,formEncType:a.encType||i,flushSync:a.flushSync})}else await e.navigate(a.action||o,{preventScrollReset:a.preventScrollReset,formData:u,body:s,formMethod:a.method||l,formEncType:a.encType||i,replace:a.replace,state:a.state,fromRouteId:n,flushSync:a.flushSync,viewTransition:a.viewTransition})}),[e,t,n])}new TextEncoder;const ut=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter(((e,t,n)=>Boolean(e)&&n.indexOf(e)===t)).join(" ")};var st={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const ct=(0,a.forwardRef)(((e,t)=>{let{color:n="currentColor",size:r=24,strokeWidth:o=2,absoluteStrokeWidth:l,className:i="",children:u,iconNode:s,...c}=e;return(0,a.createElement)("svg",{ref:t,...st,width:r,height:r,stroke:n,strokeWidth:l?24*Number(o)/Number(r):o,className:ut("lucide",i),...c},[...s.map((e=>{let[t,n]=e;return(0,a.createElement)(t,n)})),...Array.isArray(u)?u:[u]])})),dt=(e,t)=>{const n=(0,a.forwardRef)(((n,r)=>{let{className:o,...l}=n;return(0,a.createElement)(ct,{ref:r,iconNode:t,className:ut(`lucide-${i=e,i.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,o),...l});var i}));return n.displayName=`${e}`,n},ft=dt("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),pt=dt("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);function ht(e,t){return function(){return e.apply(t,arguments)}}const{toString:mt}=Object.prototype,{getPrototypeOf:gt}=Object,yt=(vt=Object.create(null),e=>{const t=mt.call(e);return vt[t]||(vt[t]=t.slice(8,-1).toLowerCase())});var vt;const bt=e=>(e=e.toLowerCase(),t=>yt(t)===e),wt=e=>t=>typeof t===e,{isArray:kt}=Array,St=wt("undefined");const Et=bt("ArrayBuffer");const xt=wt("string"),Ct=wt("function"),Nt=wt("number"),Rt=e=>null!==e&&"object"===typeof e,_t=e=>{if("object"!==yt(e))return!1;const t=gt(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},Tt=bt("Date"),Pt=bt("File"),Ot=bt("Blob"),Lt=bt("FileList"),jt=bt("URLSearchParams"),[zt,Ft,At,Mt]=["ReadableStream","Request","Response","Headers"].map(bt);function Dt(e,t){let n,r,{allOwnKeys:a=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(null!==e&&"undefined"!==typeof e)if("object"!==typeof e&&(e=[e]),kt(e))for(n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else{const r=a?Object.getOwnPropertyNames(e):Object.keys(e),o=r.length;let l;for(n=0;n<o;n++)l=r[n],t.call(null,e[l],l,e)}}function Ut(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,a=n.length;for(;a-- >0;)if(r=n[a],t===r.toLowerCase())return r;return null}const It="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:global,$t=e=>!St(e)&&e!==It;const Bt=(Wt="undefined"!==typeof Uint8Array&&gt(Uint8Array),e=>Wt&&e instanceof Wt);var Wt;const Ht=bt("HTMLFormElement"),Vt=(e=>{let{hasOwnProperty:t}=e;return(e,n)=>t.call(e,n)})(Object.prototype),qt=bt("RegExp"),Qt=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};Dt(n,((n,a)=>{let o;!1!==(o=t(n,a,e))&&(r[a]=o||n)})),Object.defineProperties(e,r)};const Kt=bt("AsyncFunction"),Jt=((e,t)=>{return e?setImmediate:t?(n=`axios@${Math.random()}`,r=[],It.addEventListener("message",(e=>{let{source:t,data:a}=e;t===It&&a===n&&r.length&&r.shift()()}),!1),e=>{r.push(e),It.postMessage(n,"*")}):e=>setTimeout(e);var n,r})("function"===typeof setImmediate,Ct(It.postMessage)),Yt="undefined"!==typeof queueMicrotask?queueMicrotask.bind(It):"undefined"!==typeof process&&process.nextTick||Jt,Xt={isArray:kt,isArrayBuffer:Et,isBuffer:function(e){return null!==e&&!St(e)&&null!==e.constructor&&!St(e.constructor)&&Ct(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"===typeof FormData&&e instanceof FormData||Ct(e.append)&&("formdata"===(t=yt(e))||"object"===t&&Ct(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&Et(e.buffer),t},isString:xt,isNumber:Nt,isBoolean:e=>!0===e||!1===e,isObject:Rt,isPlainObject:_t,isReadableStream:zt,isRequest:Ft,isResponse:At,isHeaders:Mt,isUndefined:St,isDate:Tt,isFile:Pt,isBlob:Ot,isRegExp:qt,isFunction:Ct,isStream:e=>Rt(e)&&Ct(e.pipe),isURLSearchParams:jt,isTypedArray:Bt,isFileList:Lt,forEach:Dt,merge:function e(){const{caseless:t}=$t(this)&&this||{},n={},r=(r,a)=>{const o=t&&Ut(n,a)||a;_t(n[o])&&_t(r)?n[o]=e(n[o],r):_t(r)?n[o]=e({},r):kt(r)?n[o]=r.slice():n[o]=r};for(let a=0,o=arguments.length;a<o;a++)arguments[a]&&Dt(arguments[a],r);return n},extend:function(e,t,n){let{allOwnKeys:r}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return Dt(t,((t,r)=>{n&&Ct(t)?e[r]=ht(t,n):e[r]=t}),{allOwnKeys:r}),e},trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let a,o,l;const i={};if(t=t||{},null==e)return t;do{for(a=Object.getOwnPropertyNames(e),o=a.length;o-- >0;)l=a[o],r&&!r(l,e,t)||i[l]||(t[l]=e[l],i[l]=!0);e=!1!==n&&gt(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:yt,kindOfTest:bt,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(kt(e))return e;let t=e.length;if(!Nt(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[Symbol.iterator]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:Ht,hasOwnProperty:Vt,hasOwnProp:Vt,reduceDescriptors:Qt,freezeMethods:e=>{Qt(e,((t,n)=>{if(Ct(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];Ct(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach((e=>{n[e]=!0}))};return kt(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,t,n){return t.toUpperCase()+n})),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:Ut,global:It,isContextDefined:$t,isSpecCompliantForm:function(e){return!!(e&&Ct(e.append)&&"FormData"===e[Symbol.toStringTag]&&e[Symbol.iterator])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(Rt(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const a=kt(e)?[]:{};return Dt(e,((e,t)=>{const o=n(e,r+1);!St(o)&&(a[t]=o)})),t[r]=void 0,a}}return e};return n(e,0)},isAsyncFn:Kt,isThenable:e=>e&&(Rt(e)||Ct(e))&&Ct(e.then)&&Ct(e.catch),setImmediate:Jt,asap:Yt};function Gt(e,t,n,r,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),a&&(this.response=a,this.status=a.status?a.status:null)}Xt.inherits(Gt,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:Xt.toJSONObject(this.config),code:this.code,status:this.status}}});const Zt=Gt.prototype,en={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{en[e]={value:e}})),Object.defineProperties(Gt,en),Object.defineProperty(Zt,"isAxiosError",{value:!0}),Gt.from=(e,t,n,r,a,o)=>{const l=Object.create(Zt);return Xt.toFlatObject(e,l,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),Gt.call(l,e.message,t,n,r,a),l.cause=e,l.name=e.name,o&&Object.assign(l,o),l};const tn=Gt;function nn(e){return Xt.isPlainObject(e)||Xt.isArray(e)}function rn(e){return Xt.endsWith(e,"[]")?e.slice(0,-2):e}function an(e,t,n){return e?e.concat(t).map((function(e,t){return e=rn(e),!n&&t?"["+e+"]":e})).join(n?".":""):t}const on=Xt.toFlatObject(Xt,{},null,(function(e){return/^is[A-Z]/.test(e)}));const ln=function(e,t,n){if(!Xt.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=Xt.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,t){return!Xt.isUndefined(t[e])}))).metaTokens,a=n.visitor||s,o=n.dots,l=n.indexes,i=(n.Blob||"undefined"!==typeof Blob&&Blob)&&Xt.isSpecCompliantForm(t);if(!Xt.isFunction(a))throw new TypeError("visitor must be a function");function u(e){if(null===e)return"";if(Xt.isDate(e))return e.toISOString();if(!i&&Xt.isBlob(e))throw new tn("Blob is not supported. Use a Buffer instead.");return Xt.isArrayBuffer(e)||Xt.isTypedArray(e)?i&&"function"===typeof Blob?new Blob([e]):Buffer.from(e):e}function s(e,n,a){let i=e;if(e&&!a&&"object"===typeof e)if(Xt.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(Xt.isArray(e)&&function(e){return Xt.isArray(e)&&!e.some(nn)}(e)||(Xt.isFileList(e)||Xt.endsWith(n,"[]"))&&(i=Xt.toArray(e)))return n=rn(n),i.forEach((function(e,r){!Xt.isUndefined(e)&&null!==e&&t.append(!0===l?an([n],r,o):null===l?n:n+"[]",u(e))})),!1;return!!nn(e)||(t.append(an(a,n,o),u(e)),!1)}const c=[],d=Object.assign(on,{defaultVisitor:s,convertValue:u,isVisitable:nn});if(!Xt.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!Xt.isUndefined(n)){if(-1!==c.indexOf(n))throw Error("Circular reference detected in "+r.join("."));c.push(n),Xt.forEach(n,(function(n,o){!0===(!(Xt.isUndefined(n)||null===n)&&a.call(t,n,Xt.isString(o)?o.trim():o,r,d))&&e(n,r?r.concat(o):[o])})),c.pop()}}(e),t};function un(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return t[e]}))}function sn(e,t){this._pairs=[],e&&ln(e,this,t)}const cn=sn.prototype;cn.append=function(e,t){this._pairs.push([e,t])},cn.toString=function(e){const t=e?function(t){return e.call(this,t,un)}:un;return this._pairs.map((function(e){return t(e[0])+"="+t(e[1])}),"").join("&")};const dn=sn;function fn(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function pn(e,t,n){if(!t)return e;const r=n&&n.encode||fn;Xt.isFunction(n)&&(n={serialize:n});const a=n&&n.serialize;let o;if(o=a?a(t,n):Xt.isURLSearchParams(t)?t.toString():new dn(t,n).toString(r),o){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+o}return e}const hn=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){Xt.forEach(this.handlers,(function(t){null!==t&&e(t)}))}},mn={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},gn={isBrowser:!0,classes:{URLSearchParams:"undefined"!==typeof URLSearchParams?URLSearchParams:dn,FormData:"undefined"!==typeof FormData?FormData:null,Blob:"undefined"!==typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},yn="undefined"!==typeof window&&"undefined"!==typeof document,vn="object"===typeof navigator&&navigator||void 0,bn=yn&&(!vn||["ReactNative","NativeScript","NS"].indexOf(vn.product)<0),wn="undefined"!==typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"===typeof self.importScripts,kn=yn&&window.location.href||"http://localhost",Sn={...r,...gn};const En=function(e){function t(e,n,r,a){let o=e[a++];if("__proto__"===o)return!0;const l=Number.isFinite(+o),i=a>=e.length;if(o=!o&&Xt.isArray(r)?r.length:o,i)return Xt.hasOwnProp(r,o)?r[o]=[r[o],n]:r[o]=n,!l;r[o]&&Xt.isObject(r[o])||(r[o]=[]);return t(e,n,r[o],a)&&Xt.isArray(r[o])&&(r[o]=function(e){const t={},n=Object.keys(e);let r;const a=n.length;let o;for(r=0;r<a;r++)o=n[r],t[o]=e[o];return t}(r[o])),!l}if(Xt.isFormData(e)&&Xt.isFunction(e.entries)){const n={};return Xt.forEachEntry(e,((e,r)=>{t(function(e){return Xt.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}(e),r,n,0)})),n}return null};const xn={transitional:mn,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,a=Xt.isObject(e);a&&Xt.isHTMLForm(e)&&(e=new FormData(e));if(Xt.isFormData(e))return r?JSON.stringify(En(e)):e;if(Xt.isArrayBuffer(e)||Xt.isBuffer(e)||Xt.isStream(e)||Xt.isFile(e)||Xt.isBlob(e)||Xt.isReadableStream(e))return e;if(Xt.isArrayBufferView(e))return e.buffer;if(Xt.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let o;if(a){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return ln(e,new Sn.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return Sn.isNode&&Xt.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((o=Xt.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return ln(o?{"files[]":e}:e,t&&new t,this.formSerializer)}}return a||r?(t.setContentType("application/json",!1),function(e,t,n){if(Xt.isString(e))try{return(t||JSON.parse)(e),Xt.trim(e)}catch(Jr){if("SyntaxError"!==Jr.name)throw Jr}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||xn.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(Xt.isResponse(e)||Xt.isReadableStream(e))return e;if(e&&Xt.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(Jr){if(n){if("SyntaxError"===Jr.name)throw tn.from(Jr,tn.ERR_BAD_RESPONSE,this,null,this.response);throw Jr}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Sn.classes.FormData,Blob:Sn.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};Xt.forEach(["delete","get","head","post","put","patch"],(e=>{xn.headers[e]={}}));const Cn=xn,Nn=Xt.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Rn=Symbol("internals");function _n(e){return e&&String(e).trim().toLowerCase()}function Tn(e){return!1===e||null==e?e:Xt.isArray(e)?e.map(Tn):String(e)}function Pn(e,t,n,r,a){return Xt.isFunction(r)?r.call(this,t,n):(a&&(t=n),Xt.isString(t)?Xt.isString(r)?-1!==t.indexOf(r):Xt.isRegExp(r)?r.test(t):void 0:void 0)}class On{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function a(e,t,n){const a=_n(t);if(!a)throw new Error("header name must be a non-empty string");const o=Xt.findKey(r,a);(!o||void 0===r[o]||!0===n||void 0===n&&!1!==r[o])&&(r[o||t]=Tn(e))}const o=(e,t)=>Xt.forEach(e,((e,n)=>a(e,n,t)));if(Xt.isPlainObject(e)||e instanceof this.constructor)o(e,t);else if(Xt.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))o((e=>{const t={};let n,r,a;return e&&e.split("\n").forEach((function(e){a=e.indexOf(":"),n=e.substring(0,a).trim().toLowerCase(),r=e.substring(a+1).trim(),!n||t[n]&&Nn[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)})),t})(e),t);else if(Xt.isHeaders(e))for(const[l,i]of e.entries())a(i,l,n);else null!=e&&a(t,e,n);return this}get(e,t){if(e=_n(e)){const n=Xt.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(Xt.isFunction(t))return t.call(this,e,n);if(Xt.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=_n(e)){const n=Xt.findKey(this,e);return!(!n||void 0===this[n]||t&&!Pn(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function a(e){if(e=_n(e)){const a=Xt.findKey(n,e);!a||t&&!Pn(0,n[a],a,t)||(delete n[a],r=!0)}}return Xt.isArray(e)?e.forEach(a):a(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const a=t[n];e&&!Pn(0,this[a],a,e,!0)||(delete this[a],r=!0)}return r}normalize(e){const t=this,n={};return Xt.forEach(this,((r,a)=>{const o=Xt.findKey(n,a);if(o)return t[o]=Tn(r),void delete t[a];const l=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,t,n)=>t.toUpperCase()+n))}(a):String(a).trim();l!==a&&delete t[a],t[l]=Tn(r),n[l]=!0})),this}concat(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.constructor.concat(this,...t)}toJSON(e){const t=Object.create(null);return Xt.forEach(this,((n,r)=>{null!=n&&!1!==n&&(t[r]=e&&Xt.isArray(n)?n.join(", "):n)})),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((e=>{let[t,n]=e;return t+": "+n})).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e){const t=new this(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];return r.forEach((e=>t.set(e))),t}static accessor(e){const t=(this[Rn]=this[Rn]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=_n(e);t[r]||(!function(e,t){const n=Xt.toCamelCase(" "+t);["get","set","has"].forEach((r=>{Object.defineProperty(e,r+n,{value:function(e,n,a){return this[r].call(this,t,e,n,a)},configurable:!0})}))}(n,e),t[r]=!0)}return Xt.isArray(e)?e.forEach(r):r(e),this}}On.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),Xt.reduceDescriptors(On.prototype,((e,t)=>{let{value:n}=e,r=t[0].toUpperCase()+t.slice(1);return{get:()=>n,set(e){this[r]=e}}})),Xt.freezeMethods(On);const Ln=On;function jn(e,t){const n=this||Cn,r=t||n,a=Ln.from(r.headers);let o=r.data;return Xt.forEach(e,(function(e){o=e.call(n,o,a.normalize(),t?t.status:void 0)})),a.normalize(),o}function zn(e){return!(!e||!e.__CANCEL__)}function Fn(e,t,n){tn.call(this,null==e?"canceled":e,tn.ERR_CANCELED,t,n),this.name="CanceledError"}Xt.inherits(Fn,tn,{__CANCEL__:!0});const An=Fn;function Mn(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new tn("Request failed with status code "+n.status,[tn.ERR_BAD_REQUEST,tn.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}const Dn=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let a,o=0,l=0;return t=void 0!==t?t:1e3,function(i){const u=Date.now(),s=r[l];a||(a=u),n[o]=i,r[o]=u;let c=l,d=0;for(;c!==o;)d+=n[c++],c%=e;if(o=(o+1)%e,o===l&&(l=(l+1)%e),u-a<t)return;const f=s&&u-s;return f?Math.round(1e3*d/f):void 0}};const Un=function(e,t){let n,r,a=0,o=1e3/t;const l=function(t){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now();a=o,n=null,r&&(clearTimeout(r),r=null),e.apply(null,t)};return[function(){const e=Date.now(),t=e-a;for(var i=arguments.length,u=new Array(i),s=0;s<i;s++)u[s]=arguments[s];t>=o?l(u,e):(n=u,r||(r=setTimeout((()=>{r=null,l(n)}),o-t)))},()=>n&&l(n)]},In=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3,r=0;const a=Dn(50,250);return Un((n=>{const o=n.loaded,l=n.lengthComputable?n.total:void 0,i=o-r,u=a(i);r=o;e({loaded:o,total:l,progress:l?o/l:void 0,bytes:i,rate:u||void 0,estimated:u&&l&&o<=l?(l-o)/u:void 0,event:n,lengthComputable:null!=l,[t?"download":"upload"]:!0})}),n)},$n=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Bn=e=>function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return Xt.asap((()=>e(...n)))},Wn=Sn.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,Sn.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(Sn.origin),Sn.navigator&&/(msie|trident)/i.test(Sn.navigator.userAgent)):()=>!0,Hn=Sn.hasStandardBrowserEnv?{write(e,t,n,r,a,o){const l=[e+"="+encodeURIComponent(t)];Xt.isNumber(n)&&l.push("expires="+new Date(n).toGMTString()),Xt.isString(r)&&l.push("path="+r),Xt.isString(a)&&l.push("domain="+a),!0===o&&l.push("secure"),document.cookie=l.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function Vn(e,t,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const qn=e=>e instanceof Ln?{...e}:e;function Qn(e,t){t=t||{};const n={};function r(e,t,n,r){return Xt.isPlainObject(e)&&Xt.isPlainObject(t)?Xt.merge.call({caseless:r},e,t):Xt.isPlainObject(t)?Xt.merge({},t):Xt.isArray(t)?t.slice():t}function a(e,t,n,a){return Xt.isUndefined(t)?Xt.isUndefined(e)?void 0:r(void 0,e,0,a):r(e,t,0,a)}function o(e,t){if(!Xt.isUndefined(t))return r(void 0,t)}function l(e,t){return Xt.isUndefined(t)?Xt.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function i(n,a,o){return o in t?r(n,a):o in e?r(void 0,n):void 0}const u={url:o,method:o,data:o,baseURL:l,transformRequest:l,transformResponse:l,paramsSerializer:l,timeout:l,timeoutMessage:l,withCredentials:l,withXSRFToken:l,adapter:l,responseType:l,xsrfCookieName:l,xsrfHeaderName:l,onUploadProgress:l,onDownloadProgress:l,decompress:l,maxContentLength:l,maxBodyLength:l,beforeRedirect:l,transport:l,httpAgent:l,httpsAgent:l,cancelToken:l,socketPath:l,responseEncoding:l,validateStatus:i,headers:(e,t,n)=>a(qn(e),qn(t),0,!0)};return Xt.forEach(Object.keys(Object.assign({},e,t)),(function(r){const o=u[r]||a,l=o(e[r],t[r],r);Xt.isUndefined(l)&&o!==i||(n[r]=l)})),n}const Kn=e=>{const t=Qn({},e);let n,{data:r,withXSRFToken:a,xsrfHeaderName:o,xsrfCookieName:l,headers:i,auth:u}=t;if(t.headers=i=Ln.from(i),t.url=pn(Vn(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),u&&i.set("Authorization","Basic "+btoa((u.username||"")+":"+(u.password?unescape(encodeURIComponent(u.password)):""))),Xt.isFormData(r))if(Sn.hasStandardBrowserEnv||Sn.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if(!1!==(n=i.getContentType())){const[e,...t]=n?n.split(";").map((e=>e.trim())).filter(Boolean):[];i.setContentType([e||"multipart/form-data",...t].join("; "))}if(Sn.hasStandardBrowserEnv&&(a&&Xt.isFunction(a)&&(a=a(t)),a||!1!==a&&Wn(t.url))){const e=o&&l&&Hn.read(l);e&&i.set(o,e)}return t},Jn="undefined"!==typeof XMLHttpRequest&&function(e){return new Promise((function(t,n){const r=Kn(e);let a=r.data;const o=Ln.from(r.headers).normalize();let l,i,u,s,c,{responseType:d,onUploadProgress:f,onDownloadProgress:p}=r;function h(){s&&s(),c&&c(),r.cancelToken&&r.cancelToken.unsubscribe(l),r.signal&&r.signal.removeEventListener("abort",l)}let m=new XMLHttpRequest;function g(){if(!m)return;const r=Ln.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());Mn((function(e){t(e),h()}),(function(e){n(e),h()}),{data:d&&"text"!==d&&"json"!==d?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:r,config:e,request:m}),m=null}m.open(r.method.toUpperCase(),r.url,!0),m.timeout=r.timeout,"onloadend"in m?m.onloadend=g:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(g)},m.onabort=function(){m&&(n(new tn("Request aborted",tn.ECONNABORTED,e,m)),m=null)},m.onerror=function(){n(new tn("Network Error",tn.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const a=r.transitional||mn;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new tn(t,a.clarifyTimeoutError?tn.ETIMEDOUT:tn.ECONNABORTED,e,m)),m=null},void 0===a&&o.setContentType(null),"setRequestHeader"in m&&Xt.forEach(o.toJSON(),(function(e,t){m.setRequestHeader(t,e)})),Xt.isUndefined(r.withCredentials)||(m.withCredentials=!!r.withCredentials),d&&"json"!==d&&(m.responseType=r.responseType),p&&([u,c]=In(p,!0),m.addEventListener("progress",u)),f&&m.upload&&([i,s]=In(f),m.upload.addEventListener("progress",i),m.upload.addEventListener("loadend",s)),(r.cancelToken||r.signal)&&(l=t=>{m&&(n(!t||t.type?new An(null,e,m):t),m.abort(),m=null)},r.cancelToken&&r.cancelToken.subscribe(l),r.signal&&(r.signal.aborted?l():r.signal.addEventListener("abort",l)));const y=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);y&&-1===Sn.protocols.indexOf(y)?n(new tn("Unsupported protocol "+y+":",tn.ERR_BAD_REQUEST,e)):m.send(a||null)}))},Yn=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const a=function(e){if(!n){n=!0,l();const t=e instanceof Error?e:this.reason;r.abort(t instanceof tn?t:new An(t instanceof Error?t.message:t))}};let o=t&&setTimeout((()=>{o=null,a(new tn(`timeout ${t} of ms exceeded`,tn.ETIMEDOUT))}),t);const l=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach((e=>{e.unsubscribe?e.unsubscribe(a):e.removeEventListener("abort",a)})),e=null)};e.forEach((e=>e.addEventListener("abort",a)));const{signal:i}=r;return i.unsubscribe=()=>Xt.asap(l),i}},Xn=function*(e,t){let n=e.byteLength;if(!t||n<t)return void(yield e);let r,a=0;for(;a<n;)r=a+t,yield e.slice(a,r),a=r},Gn=async function*(e){if(e[Symbol.asyncIterator])return void(yield*e);const t=e.getReader();try{for(;;){const{done:e,value:n}=await t.read();if(e)break;yield n}}finally{await t.cancel()}},Zn=(e,t,n,r)=>{const a=async function*(e,t){for await(const n of Gn(e))yield*Xn(n,t)}(e,t);let o,l=0,i=e=>{o||(o=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await a.next();if(t)return i(),void e.close();let o=r.byteLength;if(n){let e=l+=o;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw i(t),t}},cancel:e=>(i(e),a.return())},{highWaterMark:2})},er="function"===typeof fetch&&"function"===typeof Request&&"function"===typeof Response,tr=er&&"function"===typeof ReadableStream,nr=er&&("function"===typeof TextEncoder?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),rr=function(e){try{for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return!!e(...n)}catch(Jr){return!1}},ar=tr&&rr((()=>{let e=!1;const t=new Request(Sn.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t})),or=tr&&rr((()=>Xt.isReadableStream(new Response("").body))),lr={stream:or&&(e=>e.body)};var ir;er&&(ir=new Response,["text","arrayBuffer","blob","formData","stream"].forEach((e=>{!lr[e]&&(lr[e]=Xt.isFunction(ir[e])?t=>t[e]():(t,n)=>{throw new tn(`Response type '${e}' is not supported`,tn.ERR_NOT_SUPPORT,n)})})));const ur=async(e,t)=>{const n=Xt.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if(Xt.isBlob(e))return e.size;if(Xt.isSpecCompliantForm(e)){const t=new Request(Sn.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return Xt.isArrayBufferView(e)||Xt.isArrayBuffer(e)?e.byteLength:(Xt.isURLSearchParams(e)&&(e+=""),Xt.isString(e)?(await nr(e)).byteLength:void 0)})(t):n},sr=er&&(async e=>{let{url:t,method:n,data:r,signal:a,cancelToken:o,timeout:l,onDownloadProgress:i,onUploadProgress:u,responseType:s,headers:c,withCredentials:d="same-origin",fetchOptions:f}=Kn(e);s=s?(s+"").toLowerCase():"text";let p,h=Yn([a,o&&o.toAbortSignal()],l);const m=h&&h.unsubscribe&&(()=>{h.unsubscribe()});let g;try{if(u&&ar&&"get"!==n&&"head"!==n&&0!==(g=await ur(c,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if(Xt.isFormData(r)&&(e=n.headers.get("content-type"))&&c.setContentType(e),n.body){const[e,t]=$n(g,In(Bn(u)));r=Zn(n.body,65536,e,t)}}Xt.isString(d)||(d=d?"include":"omit");const a="credentials"in Request.prototype;p=new Request(t,{...f,signal:h,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:r,duplex:"half",credentials:a?d:void 0});let o=await fetch(p);const l=or&&("stream"===s||"response"===s);if(or&&(i||l&&m)){const e={};["status","statusText","headers"].forEach((t=>{e[t]=o[t]}));const t=Xt.toFiniteNumber(o.headers.get("content-length")),[n,r]=i&&$n(t,In(Bn(i),!0))||[];o=new Response(Zn(o.body,65536,n,(()=>{r&&r(),m&&m()})),e)}s=s||"text";let y=await lr[Xt.findKey(lr,s)||"text"](o,e);return!l&&m&&m(),await new Promise(((t,n)=>{Mn(t,n,{data:y,headers:Ln.from(o.headers),status:o.status,statusText:o.statusText,config:e,request:p})}))}catch(y){if(m&&m(),y&&"TypeError"===y.name&&/fetch/i.test(y.message))throw Object.assign(new tn("Network Error",tn.ERR_NETWORK,e,p),{cause:y.cause||y});throw tn.from(y,y&&y.code,e,p)}}),cr={http:null,xhr:Jn,fetch:sr};Xt.forEach(cr,((e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(Jr){}Object.defineProperty(e,"adapterName",{value:t})}}));const dr=e=>`- ${e}`,fr=e=>Xt.isFunction(e)||null===e||!1===e,pr=e=>{e=Xt.isArray(e)?e:[e];const{length:t}=e;let n,r;const a={};for(let o=0;o<t;o++){let t;if(n=e[o],r=n,!fr(n)&&(r=cr[(t=String(n)).toLowerCase()],void 0===r))throw new tn(`Unknown adapter '${t}'`);if(r)break;a[t||"#"+o]=r}if(!r){const e=Object.entries(a).map((e=>{let[t,n]=e;return`adapter ${t} `+(!1===n?"is not supported by the environment":"is not available in the build")}));let n=t?e.length>1?"since :\n"+e.map(dr).join("\n"):" "+dr(e[0]):"as no adapter specified";throw new tn("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return r};function hr(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new An(null,e)}function mr(e){hr(e),e.headers=Ln.from(e.headers),e.data=jn.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return pr(e.adapter||Cn.adapter)(e).then((function(t){return hr(e),t.data=jn.call(e,e.transformResponse,t),t.headers=Ln.from(t.headers),t}),(function(t){return zn(t)||(hr(e),t&&t.response&&(t.response.data=jn.call(e,e.transformResponse,t.response),t.response.headers=Ln.from(t.response.headers))),Promise.reject(t)}))}const gr="1.8.4",yr={};["object","boolean","number","function","string","symbol"].forEach(((e,t)=>{yr[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));const vr={};yr.transitional=function(e,t,n){function r(e,t){return"[Axios v1.8.4] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,a,o)=>{if(!1===e)throw new tn(r(a," has been removed"+(t?" in "+t:"")),tn.ERR_DEPRECATED);return t&&!vr[a]&&(vr[a]=!0,console.warn(r(a," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,a,o)}},yr.spelling=function(e){return(t,n)=>(console.warn(`${n} is likely a misspelling of ${e}`),!0)};const br={assertOptions:function(e,t,n){if("object"!==typeof e)throw new tn("options must be an object",tn.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let a=r.length;for(;a-- >0;){const o=r[a],l=t[o];if(l){const t=e[o],n=void 0===t||l(t,o,e);if(!0!==n)throw new tn("option "+o+" must be "+n,tn.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new tn("Unknown option "+o,tn.ERR_BAD_OPTION)}},validators:yr},wr=br.validators;class kr{constructor(e){this.defaults=e,this.interceptors={request:new hn,response:new hn}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(Jr){}}throw n}}_request(e,t){"string"===typeof e?(t=t||{}).url=e:t=e||{},t=Qn(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:a}=t;void 0!==n&&br.assertOptions(n,{silentJSONParsing:wr.transitional(wr.boolean),forcedJSONParsing:wr.transitional(wr.boolean),clarifyTimeoutError:wr.transitional(wr.boolean)},!1),null!=r&&(Xt.isFunction(r)?t.paramsSerializer={serialize:r}:br.assertOptions(r,{encode:wr.function,serialize:wr.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),br.assertOptions(t,{baseUrl:wr.spelling("baseURL"),withXsrfToken:wr.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let o=a&&Xt.merge(a.common,a[t.method]);a&&Xt.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete a[e]})),t.headers=Ln.concat(o,a);const l=[];let i=!0;this.interceptors.request.forEach((function(e){"function"===typeof e.runWhen&&!1===e.runWhen(t)||(i=i&&e.synchronous,l.unshift(e.fulfilled,e.rejected))}));const u=[];let s;this.interceptors.response.forEach((function(e){u.push(e.fulfilled,e.rejected)}));let c,d=0;if(!i){const e=[mr.bind(this),void 0];for(e.unshift.apply(e,l),e.push.apply(e,u),c=e.length,s=Promise.resolve(t);d<c;)s=s.then(e[d++],e[d++]);return s}c=l.length;let f=t;for(d=0;d<c;){const e=l[d++],t=l[d++];try{f=e(f)}catch(p){t.call(this,p);break}}try{s=mr.call(this,f)}catch(p){return Promise.reject(p)}for(d=0,c=u.length;d<c;)s=s.then(u[d++],u[d++]);return s}getUri(e){return pn(Vn((e=Qn(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}Xt.forEach(["delete","get","head","options"],(function(e){kr.prototype[e]=function(t,n){return this.request(Qn(n||{},{method:e,url:t,data:(n||{}).data}))}})),Xt.forEach(["post","put","patch"],(function(e){function t(t){return function(n,r,a){return this.request(Qn(a||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}kr.prototype[e]=t(),kr.prototype[e+"Form"]=t(!0)}));const Sr=kr;class Er{constructor(e){if("function"!==typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise((function(e){t=e}));const n=this;this.promise.then((e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null})),this.promise.then=e=>{let t;const r=new Promise((e=>{n.subscribe(e),t=e})).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e((function(e,r,a){n.reason||(n.reason=new An(e,r,a),t(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new Er((function(t){e=t})),cancel:e}}}const xr=Er;const Cr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Cr).forEach((e=>{let[t,n]=e;Cr[n]=t}));const Nr=Cr;const Rr=function e(t){const n=new Sr(t),r=ht(Sr.prototype.request,n);return Xt.extend(r,Sr.prototype,n,{allOwnKeys:!0}),Xt.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(Qn(t,n))},r}(Cn);Rr.Axios=Sr,Rr.CanceledError=An,Rr.CancelToken=xr,Rr.isCancel=zn,Rr.VERSION=gr,Rr.toFormData=ln,Rr.AxiosError=tn,Rr.Cancel=Rr.CanceledError,Rr.all=function(e){return Promise.all(e)},Rr.spread=function(e){return function(t){return e.apply(null,t)}},Rr.isAxiosError=function(e){return Xt.isObject(e)&&!0===e.isAxiosError},Rr.mergeConfig=Qn,Rr.AxiosHeaders=Ln,Rr.formToJSON=e=>En(Xt.isHTMLForm(e)?new FormData(e):e),Rr.getAdapter=pr,Rr.HttpStatusCode=Nr,Rr.default=Rr;const _r="https://publisher.orderbuzz.chakril.site",Tr="bc-credentials",Pr="Quickbuzz",Or=Rr.create({baseURL:_r,headers:{"Content-Type":"application/json",Authorization:"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImhhcnNoYSIsImlhdCI6MTc0MzU3OTc5Nn0.n0HqCaKAKFYF9SSWpoydaDCVPWkHJ-s6-Rxl65hP2VA"}}),Lr=async e=>{try{return await Or.post("/auth",e)}catch(t){throw t}},jr=async e=>{try{return await Or.post("/update-view-count",e)}catch(t){throw t}},zr=async e=>{console.log("data",e);try{return await Or.post("/get-events",e)}catch(t){throw t}};var Fr=n(579);const Ar=()=>{const e=te(),[t,n]=(0,a.useState)(""),[r,o]=(0,a.useState)(""),[l,i]=(0,a.useState)(""),[u,s]=(0,a.useState)(!0),[c,d]=(0,a.useState)(!1);(0,a.useEffect)((()=>{localStorage.getItem(Tr)&&e("/dashboard")}),[e]);return(0,Fr.jsx)("div",{className:"app "+(u?"dark":"light"),children:(0,Fr.jsxs)("div",{className:"container",children:[(0,Fr.jsxs)("div",{className:"header",children:[(0,Fr.jsx)("div",{className:"logo",children:(0,Fr.jsx)("h1",{children:Pr})}),(0,Fr.jsx)("button",{onClick:()=>{s(!u)},className:"theme-toggle",children:u?(0,Fr.jsx)(ft,{size:24}):(0,Fr.jsx)(pt,{size:24})})]}),(0,Fr.jsx)("div",{className:"login-content",children:(0,Fr.jsxs)("div",{className:"card",children:[(0,Fr.jsx)("h2",{children:"Welcome Back"}),(0,Fr.jsxs)("form",{onSubmit:async n=>{if(n.preventDefault(),d(!0),!(e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e))(t))return i("Please enter a valid email address"),void d(!1);if(r.length<6)return i("Secret key must be at least 6 characters long"),void d(!1);try{const n=await Lr({email:t,secret_key:r});if(200===n.status){var a,o;const l={email:t,secret_key:r,store:(null===n||void 0===n||null===(a=n.data)||void 0===a||null===(o=a.data)||void 0===o?void 0:o.store)||""};localStorage.setItem(Tr,JSON.stringify(l)),e("/dashboard")}}catch(l){var u,s;i((null===(u=l.response)||void 0===u||null===(s=u.data)||void 0===s?void 0:s.message)||"Authentication failed. Please try again.")}finally{d(!1)}},children:[(0,Fr.jsxs)("div",{className:"form-group",children:[(0,Fr.jsx)("label",{htmlFor:"email",children:"Email Address"}),(0,Fr.jsx)("input",{type:"email",id:"email",value:t,onChange:e=>n(e.target.value),placeholder:"Enter your email",required:!0})]}),(0,Fr.jsxs)("div",{className:"form-group",children:[(0,Fr.jsx)("label",{htmlFor:"secretKey",children:"Secret Key"}),(0,Fr.jsx)("input",{type:"password",id:"secretKey",value:r,onChange:e=>o(e.target.value),placeholder:"Enter your secret key",required:!0})]}),l&&(0,Fr.jsx)("div",{className:"error-alert",children:(0,Fr.jsx)("span",{children:l})}),(0,Fr.jsx)("button",{type:"submit",className:"verify-button",disabled:c,children:c?"Verifying...":"Verify Access"})]})]})})]})})},Mr=dt("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Dr=dt("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]),Ur=dt("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),Ir=dt("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),$r=e=>Array.isArray(e)?(0,Fr.jsx)("div",{className:"array-value",children:e.map(((e,t)=>(0,Fr.jsx)("div",{className:"array-item",children:"object"===typeof e?Object.entries(e).map((e=>{let[t,n]=e;return(0,Fr.jsxs)("div",{className:"sub-item",children:[(0,Fr.jsxs)("span",{className:"sub-key",children:[Br(t),":"]}),(0,Fr.jsx)("span",{className:"sub-value",children:$r(n)})]},t)})):String(e)},t)))}):e&&"object"===typeof e?(0,Fr.jsx)("div",{className:"object-value",children:Object.entries(e).map((e=>{let[t,n]=e;return(0,Fr.jsxs)("div",{className:"sub-item",children:[(0,Fr.jsxs)("span",{className:"sub-key",children:[t,":"]}),(0,Fr.jsx)("span",{className:"sub-value",children:$r(n)})]},t)}))}):String(e),Br=e=>e.charAt(0).toUpperCase()+e.slice(1).replace(/_/g," "),Wr=e=>{let{data:t,onBack:n}=e;const r=Object.entries(t||{});return(0,Fr.jsxs)("div",{className:"detail-view",children:[(0,Fr.jsxs)("div",{className:"detail-header",children:[(0,Fr.jsxs)("button",{onClick:n,className:"back-button",children:[(0,Fr.jsx)(Ir,{size:24}),(0,Fr.jsx)("span",{children:"Back"})]}),(0,Fr.jsx)("h2",{children:"Detailed Information"})]}),(0,Fr.jsx)("div",{className:"detail-table-wrapper",children:(0,Fr.jsx)("table",{className:"detail-table",children:(0,Fr.jsx)("tbody",{children:r.map((e=>{let[t,n]=e;return"timestamp"===t?null:(0,Fr.jsxs)("tr",{children:[(0,Fr.jsx)("td",{className:"key-cell",children:Br(t)}),(0,Fr.jsx)("td",{className:"value-cell",children:$r(n)})]},t)}))})})})]})},Hr=(0,a.memo)((e=>{let{message:t,id:n,timestamp:r,onClose:a,onClick:o,eventId:l}=e;return(0,Fr.jsxs)("div",{className:"notification",onClick:()=>o({id:n,message:t,timestamp:r,eventId:l}),children:[(0,Fr.jsx)("span",{children:t}),(0,Fr.jsx)("button",{onClick:e=>{e.stopPropagation(),a({id:n,message:t,timestamp:r,eventId:l})},className:"close-button",children:(0,Fr.jsx)(Mr,{size:16})})]})})),Vr=["ID","Message","Topic","Actions"],qr=()=>{const e=te(),[t,n]=(0,a.useState)(!0),[r,o]=(0,a.useState)([]),[l,i]=(0,a.useState)([]),[u,s]=(0,a.useState)(null),[c,d]=(0,a.useState)(!1),f=(0,a.useCallback)((()=>n((e=>!e))),[]),p=(0,a.useCallback)((e=>{const t=JSON.parse(localStorage.getItem(Tr)),n=null===t||void 0===t?void 0:t.email;e.viewed_by?Array.isArray(e.viewed_by)&&!e.viewed_by.includes(n)&&m(e.eventId):m(e.eventId),s(e),d(!0)}),[]),h=(0,a.useCallback)((()=>{d(!1),s(null)}),[]),m=async e=>{try{const t=JSON.parse(localStorage.getItem(Tr)),n=null===t||void 0===t?void 0:t.email,r=null===t||void 0===t?void 0:t.store;if(n&&r){(await jr({email:n,id:e,store_name:r})).status}}catch(t){}},g=(0,a.useCallback)((async(e,t)=>{const n=JSON.parse(localStorage.getItem(Tr)),r=null===n||void 0===n?void 0:n.email;t.viewed_by&&Array.isArray(t.viewed_by)&&t.viewed_by.includes(r)||await m(t.eventId),o((t=>t.filter((t=>t.eventId!==e))))}),[]),y=(0,a.useCallback)((e=>{const t=l.find((t=>t.id===e.id));t&&(p(t.data),g(e.eventId,e))}),[l,p,g]),v=(0,a.useCallback)((()=>{localStorage.removeItem(Tr),e("/login")}),[e]);return(0,a.useEffect)((()=>{(async()=>{try{const n=JSON.parse(localStorage.getItem(Tr)),r=null===n||void 0===n?void 0:n.email,a=null===n||void 0===n?void 0:n.store;if(r&&a){const n=await zr({email:r,store_name:a});if(200===n.status){var e,t;const r=(null===n||void 0===n||null===(e=n.data)||void 0===e||null===(t=e.data)||void 0===t?void 0:t.data)||[];i(r)}}}catch(n){console.error("Error fetching events:",n)}})()}),[]),(0,a.useEffect)((()=>{const e=new EventSource(`${_r}/events`);return e.onmessage=e=>{try{var t;const n=JSON.parse(e.data),r=Object.values(n)[0]||n,a=JSON.parse(localStorage.getItem(Tr));if(null===a||void 0===a||!a.email)return;let l=[];if("string"===typeof(null===r||void 0===r?void 0:r.check_email))l=r.check_email.split(",").map((e=>e.trim()));else{if(!Array.isArray(null===r||void 0===r?void 0:r.check_email))return void console.warn("check_email is not a string or an array:",null===r||void 0===r?void 0:r.check_email);l=r.check_email}if(!l.includes(a.email))return;const{check_email:u,...s}=r,c=(null===s||void 0===s?void 0:s.message)||("object"===typeof s?(null===(t=Object.values(s)[0])||void 0===t?void 0:t.message)||JSON.stringify(s):s);if(c.toLowerCase().includes("connection"))return;const d=(new Date).toLocaleTimeString(),f=Date.now();o((e=>[...e,{id:f,message:c,timestamp:d,eventId:r.eventId}])),i((e=>[...e,{id:f,timestamp:d,data:s}]))}catch(n){}},e.onerror=e=>console.error("SSE connection error:",e),()=>e.close()}),[]),(0,Fr.jsx)("div",{className:"app "+(t?"dark":"light"),children:(0,Fr.jsxs)("div",{className:"container",children:[(0,Fr.jsxs)("div",{className:"header",children:[(0,Fr.jsxs)("div",{className:"logo",children:[(0,Fr.jsx)("div",{className:"logo-circle"}),(0,Fr.jsx)("h1",{children:Pr})]}),(0,Fr.jsxs)("div",{className:"header-actions",children:[(0,Fr.jsx)("button",{onClick:f,className:"theme-toggle",children:t?(0,Fr.jsx)(ft,{size:24}):(0,Fr.jsx)(pt,{size:24})}),(0,Fr.jsx)("button",{onClick:v,className:"logout-button",children:(0,Fr.jsx)(Dr,{size:24})})]})]}),(0,Fr.jsx)("div",{className:"notifications-container",children:r.length>0&&r.map((e=>(0,Fr.jsx)(Hr,{eventId:e.eventId,...e,onClose:t=>g(e.eventId,t),onClick:y},e.eventId)))}),(0,Fr.jsx)("div",{children:(0,Fr.jsx)("div",{className:"card full-width",children:(0,Fr.jsx)("div",{className:"table-container",children:c?(0,Fr.jsx)(Wr,{data:u,onBack:h}):(0,Fr.jsx)(Fr.Fragment,{children:l.length>0?(0,Fr.jsxs)("table",{className:"events-table",children:[(0,Fr.jsx)("thead",{children:(0,Fr.jsx)("tr",{children:Vr.map((e=>(0,Fr.jsx)("th",{children:e},e)))})}),(0,Fr.jsx)("tbody",{children:l.map((e=>{var t,n;return(0,Fr.jsxs)("tr",{children:[(0,Fr.jsx)("td",{children:e.id}),(0,Fr.jsx)("td",{children:(null===(t=e.data)||void 0===t?void 0:t.message)||"-"}),(0,Fr.jsx)("td",{children:(null===(n=e.data)||void 0===n?void 0:n.topic)||"-"}),(0,Fr.jsx)("td",{children:(0,Fr.jsx)("button",{onClick:()=>p(e),className:"eye-button",children:(0,Fr.jsx)(Ur,{size:16})})})]},e.eventId)}))})]}):(0,Fr.jsx)("div",{className:"empty-table",children:"No events have been received yet"})})})})})]})})},Qr=()=>(0,Fr.jsx)(Ze,{children:(0,Fr.jsx)("div",{className:"app",children:(0,Fr.jsxs)(be,{children:[(0,Fr.jsx)(ye,{path:"/",element:(0,Fr.jsx)(ge,{to:"/login",replace:!0})}),(0,Fr.jsx)(ye,{path:"/login",element:(0,Fr.jsx)(Ar,{})}),(0,Fr.jsx)(ye,{path:"/dashboard",element:(0,Fr.jsx)(qr,{})}),(0,Fr.jsx)(ye,{path:"*",element:(0,Fr.jsx)(ge,{to:"/login",replace:!0})})]})})}),Kr=e=>{e&&e instanceof Function&&n.e(453).then(n.bind(n,453)).then((t=>{let{getCLS:n,getFID:r,getFCP:a,getLCP:o,getTTFB:l}=t;n(e),r(e),a(e),o(e),l(e)}))};o.createRoot(document.getElementById("root")).render((0,Fr.jsx)(a.StrictMode,{children:(0,Fr.jsx)(Qr,{})})),Kr()})();
//# sourceMappingURL=main.c567c08b.js.map