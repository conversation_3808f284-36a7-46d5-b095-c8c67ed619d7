{"version": 3, "file": "static/css/main.1726d3a0.css", "mappings": "AAAA,KAKE,kCAAmC,CACnC,iCAAkC,CAJlC,mIAEY,CAHZ,QAMF,CAEA,KACE,uEAEF,CCZA,iBAGI,kBAAmB,CAEnB,wBAAyB,CAJzB,YAAa,CACb,sBAAuB,CAEvB,gBAEF,CAEA,WACE,qBAAuB,CAEvB,iBAAkB,CAClB,8BAAwC,CAExC,eAAgB,CAJhB,YAAa,CAGb,UAEF,CAEA,cAGE,aAAc,CADd,kBAAmB,CADnB,iBAGF,CAEA,eAEE,gBAAiB,CADjB,eAAgB,CAEhB,cAEF,CAEA,qBAHE,qBAUF,CAPA,MACE,yBAA0B,CAC1B,kBAAmB,CAEnB,8BAAwC,CADxC,YAAa,CAEb,UAEF,CAEA,SAGE,uBAAwB,CADxB,kBAAmB,CADnB,iBAGF,CAEA,YAGE,qBAAsB,CAFtB,oBAAqB,CACrB,UAEF,CAEA,kBAIE,uBAAwB,CAHxB,aAAc,CAEd,eAAgB,CADhB,mBAGF,CAEA,kBAKE,yBAA0B,CAF1B,oCAAqC,CACrC,iBAAkB,CAKlB,qBAAsB,CAHtB,uBAAwB,CACxB,cAAe,CACf,YAAa,CANb,cAAgB,CAQhB,2BAA6B,CAT7B,UAUF,CAEA,eAIE,+BAAgC,CAEhC,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAKZ,cAAe,CAFf,cAAe,CACf,eAAgB,CANhB,eAAgB,CADhB,YAAa,CASb,+BAAiC,CAVjC,UAWF,CAEA,qBACE,oCACF,CAEA,aACE,gCAAiC,CAGjC,iBAAkB,CAFlB,wBAAyB,CAGzB,kBAAmB,CAFnB,cAGF,CAGA,MACE,uBAAwB,CACxB,4BAA6B,CAC7B,iBAAkB,CAClB,sBAAuB,CACvB,iBAAqB,CACrB,oBAAqB,CACrB,qBACF,CAGA,OACE,uBAAwB,CACxB,4BAA6B,CAC7B,cAAkB,CAClB,sBAAuB,CACvB,iBAAqB,CACrB,oBAAqB,CACrB,qBACF,CAEA,KAEE,qBAAsB,CADtB,UAEF,CC1HF,KACE,gBAAiB,CACjB,YAAa,CACb,yCACF,CAEA,UACE,wBAAyB,CACzB,UACF,CAEA,WACE,wBAAyB,CACzB,aACF,CAEA,WAEE,aAAc,CADd,gBAEF,CAEA,QAEE,6BAA8B,CAE9B,kBACF,CAEA,cAJE,kBAAmB,CAFnB,YASF,CAEA,aAGE,wBAAyB,CACzB,iBAAkB,CAFlB,WAAY,CAGZ,iBAAkB,CAJlB,UAKF,CAEA,cACE,eAAgB,CAChB,WAAY,CAGZ,iBAAkB,CAFlB,cAAe,CACf,WAEF,CAEA,oBACE,wBACF,CAEA,qBACE,wBACF,CAEA,MAEE,wBAAyB,CACzB,iBAAkB,CAFlB,YAAa,CAIb,kBAAmB,CADnB,eAEF,CAEA,YAGE,eAAgB,CADhB,WAAY,CAEZ,aAAc,CACd,cAAe,CAJf,iBAAkB,CAKlB,+BACF,CAEA,mBACE,wBAAyB,CACzB,aACF,CAEA,oGAQE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAEF,CAEA,MACE,wBAAyB,CACzB,iBAAkB,CAElB,8BAAwC,CADxC,YAEF,CAEA,aACE,qBACF,CAEA,SAEE,kBAAmB,CADnB,YAEF,CAEA,YAGE,aAAc,CAFd,cAAe,CACf,eAAiB,CAEjB,aACF,CAEA,eACE,aAAc,CACd,cACF,CAEA,YACE,gBACF,CAEA,qBAIE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,eACE,wBAAyB,CAEzB,WAAY,CAEZ,iBAAkB,CAHlB,aAAc,CAId,cAAe,CAFf,gBAGF,CAEA,oBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,6BAEF,CAEA,8CAIE,wBAAyB,CACzB,eAAgB,CAFhB,UAGF,CAEA,8GAME,wBAAyB,CACzB,WAAY,CACZ,eACF,CAEA,uDAGE,wBAAyB,CACzB,aACF,CAEA,4EAGE,wBAAyB,CACzB,aACF,CAEA,yBACE,cAAe,CAEf,UAAW,CADX,QAAS,CAET,YACF,CAEA,cAQE,kBAAmB,CAEnB,8BAAgC,CAThC,wBAAyB,CAGzB,iBAAkB,CAKlB,8BAAwC,CAPxC,UAAY,CASZ,cAAe,CALf,YAAa,CACb,6BAA8B,CAF9B,kBAAmB,CAFnB,iBASF,CAEA,qBACE,wBAAyB,CACzB,aACF,CAEA,4BACE,eAAgB,CAChB,WAAY,CACZ,aAAc,CACd,cAAe,CAEf,gBAAiB,CADjB,SAEF,CAEA,mBACE,GAEE,SAAU,CADV,0BAEF,CACA,GAEE,SAAU,CADV,uBAEF,CACF,CAEA,yBAUE,wHACE,yBACF,CAEA,MACE,cACF,CAEA,YACE,WAAY,CACZ,iBACF,CACF,CAEA,iBACE,gBAAiB,CAEjB,eAAgB,CADhB,eAEF,CAEA,cAEE,wBAAyB,CADzB,UAEF,CAEA,iBAGE,wCAAyC,CAFzC,eAAgB,CAChB,KAAM,CAEN,SACF,CAEA,kCAIE,oCAAqC,CAFrC,WAAY,CACZ,eAEF,CAEA,iBACE,oBAAqB,CACrB,qBACF,CAEA,YACE,eAAgB,CAChB,WAAY,CAGZ,iBAAkB,CAFlB,cAAe,CACf,WAAY,CAEZ,+BACF,CAEA,kBACE,0BACF,CAEA,kBACE,UACF,CAEA,wBACE,0BACF,CAGA,aAEE,WAAY,CADZ,UAEF,CAEA,eAEE,kBAAmB,CADnB,YAAa,CAGb,QAAS,CADT,kBAAmB,CAEnB,cACF,CAEA,kBAEE,gBAAiB,CADjB,QAEF,CAEA,aAEE,kBAAmB,CAQnB,WAAY,CALZ,iBAAkB,CAClB,cAAe,CALf,YAAa,CAOb,cAAe,CACf,eAAgB,CANhB,OAAQ,CACR,iBAAkB,CAGlB,uBAIF,CAGA,oBACE,kBAAmB,CACnB,aACF,CAEA,0BACE,kBAAmB,CACnB,0BACF,CAEA,yBACE,aACF,CAEA,qBACE,eAAmB,CACnB,wBACF,CAEA,iBACE,wBAAyB,CACzB,aACF,CAEA,mBACE,aACF,CAEA,mBACE,kBAAmB,CACnB,wBACF,CAEA,gBACE,aACF,CAEA,kBACE,aACF,CAGA,mBACE,kBAAmB,CACnB,aACF,CAEA,yBACE,kBAAmB,CACnB,0BACF,CAEA,wBACE,aACF,CAEA,oBACE,kBAAmB,CACnB,wBACF,CAEA,gBACE,wBAAyB,CAEzB,+BAAgC,CADhC,aAEF,CAEA,kBAEE,+BAAgC,CADhC,aAEF,CAEA,kBACE,kBAAmB,CACnB,wBACF,CAEA,eACE,aACF,CAEA,iBACE,aACF,CAGA,sBACE,cACF,CAEA,cAEE,uBAAyB,CAEzB,iBAAkB,CADlB,gBAAiB,CAEjB,eAAgB,CAJhB,UAKF,CAEA,UAGE,eAAgB,CADhB,YAAa,CADb,SAGF,CAEA,YAEE,YAAa,CADb,SAEF,CAEA,2BAEE,YAAa,CACb,qBAAsB,CACtB,OACF,CAEA,YAEE,iBAAkB,CAClB,YAAa,CAFb,YAGF,CAEA,UACE,YAAa,CACb,QAAS,CACT,aACF,CAEA,SACE,eAAgB,CAChB,eACF,CAGA,iCACE,wBAAyB,CACzB,aACF,CAEA,6BACE,wBACF,CAEA,8BACE,wBACF,CAGA,8BAEE,8BACF,CAEA,kCAIE,UAAW,CACX,iBAAkB,CAFlB,YAAa,CADb,iBAIF,CAEA,aACE,oBAA+B,CAC/B,iBAAkB,CAClB,aACF,CAEA,gBAGE,kBAAmB,CAFnB,YAAa,CACb,QAEF,CAEA,eACE,eAAgB,CAChB,WAAY,CAIZ,iBAAkB,CAFlB,aAAc,CADd,cAAe,CAEf,WAAY,CAEZ,+BACF,CAEA,qBACE,0BACF,CAEA,2BACE,0BACF,CAMA,4BAQE,kBAAmB,CAGnB,6BAA+B,CAL/B,oBAA8B,CAD9B,QAAS,CAET,YAAa,CAEb,sBAAuB,CANvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,aAEF,CAEA,oBASE,gCAAkC,CARlC,eAAiB,CACjB,kBAAmB,CAMnB,gCAA0C,CAF1C,eAAgB,CAFhB,eAAgB,CAGhB,eAAgB,CAJhB,SAAU,CAOV,iBAAkB,CALlB,SAMF,CAEA,0BACE,kBAAmB,CACnB,UACF,CAEA,2BAEE,mBAAyB,CADzB,iBAEF,CAEA,oBAIE,eAAgB,CAChB,WAAY,CAGZ,iBAAkB,CAClB,aAAc,CAHd,cAAe,CACf,WAAY,CANZ,iBAAkB,CAElB,UAAW,CADX,QAAS,CAQT,uBACF,CAEA,0BACE,oBAA8B,CAC9B,aACF,CAEA,0BACE,aACF,CAEA,gCACE,oBAAoC,CACpC,UACF,CAEA,4BACE,iBAAkB,CAClB,iBACF,CAEA,YAEE,aAAc,CADd,kBAEF,CAEA,mBACE,aACF,CAEA,oBAEE,2BAA4B,CAD5B,aAEF,CAEA,+BAIE,aAAc,CAFd,cAAe,CACf,eAAgB,CAFhB,eAIF,CAEA,qCACE,UACF,CAEA,8BAEE,aAAc,CAEd,cAAe,CADf,eAAgB,CAFhB,eAIF,CAEA,oCACE,aACF,CAEA,2BACE,kBAAmB,CACnB,iBAAkB,CAElB,aAAc,CADd,YAAa,CAEb,eACF,CAEA,iCACE,kBACF,CAEA,kBAEE,aAAc,CACd,cAAe,CACf,eAAgB,CAHhB,YAIF,CAEA,wBACE,aACF,CAEA,4BAEE,YAAa,CAGb,cAAe,CAFf,QAAS,CACT,sBAAuB,CAHvB,sBAKF,CAEA,8CAEE,kBAAmB,CAGnB,WAAY,CACZ,iBAAkB,CAElB,cAAe,CAPf,YAAa,CASb,cAAe,CAHf,eAAgB,CAJhB,OAAQ,CASR,sBAAuB,CADvB,eAAgB,CAPhB,iBAAkB,CAKlB,uBAIF,CAEA,sBACE,kBAAmB,CACnB,UACF,CAEA,4BACE,kBAAmB,CACnB,0BACF,CAEA,wBACE,kBAAmB,CACnB,aACF,CAEA,8BACE,kBACF,CAEA,8BACE,kBAAmB,CACnB,aACF,CAEA,oCACE,kBACF,CAEA,kBACE,GACE,SACF,CACA,GACE,SACF,CACF,CAEA,qBACE,GACE,SAAU,CACV,qCACF,CACA,GACE,SAAU,CACV,gCACF,CACF,CAEA,iBACE,MACE,kBACF,CACA,IACE,oBACF,CACF,CC7tBA,YACE,uCACF,CAEA,sBACE,4BAA6B,CAC7B,eAAgB,CAChB,SACF,CAEA,iBAGE,sBAAmB,CAAnB,kBAAmB,CACnB,WAAY,CAIZ,cAAe,CAPf,oBAAqB,CAKrB,eAAgB,CAChB,gBAAiB,CAFjB,iBAIF,CAEA,2BACE,eAAgB,CAChB,iBAAkB,CAElB,yBAA0B,CAD1B,UAEF,CAEA,2BACE,cAAe,CACf,cACF,CAEA,uBACE,YACF,CAEA,6BAOE,eAAgB,CADhB,WAAY,CALZ,UAAW,CAEX,UAAW,CACX,SAAU,CAFV,iBAAkB,CAGlB,UAGF,CAEA,uBACE,YACF,CAEA,iCACE,aACF,CCrDA,KACE,iBACF,CAEA,UACE,aAAc,CACd,mBACF,CAEA,8CACE,UACE,2CACF,CACF,CAEA,YAKE,kBAAmB,CAJnB,wBAAyB,CAOzB,UAAY,CALZ,YAAa,CACb,qBAAsB,CAGtB,4BAA6B,CAD7B,sBAAuB,CAJvB,gBAOF,CAEA,UACE,aACF,CAEA,yBACE,GACE,sBACF,CACA,GACE,uBACF,CACF", "sources": ["index.css", "components/Login.css", "components/Dashboard.css", "../node_modules/react-tabs/style/react-tabs.css", "App.css"], "sourcesContent": ["body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n", ".login-container {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    min-height: 100vh;\n    background-color: #f7fafc;\n  }\n  \n  .login-box {\n    background-color: white;\n    padding: 2rem;\n    border-radius: 8px;\n    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n    width: 100%;\n    max-width: 400px;\n  }\n  \n  .login-box h2 {\n    text-align: center;\n    margin-bottom: 2rem;\n    color: #2d3748;\n  }\n  \n  .login-content {\n    max-width: 500px;\n    margin: 4rem auto;\n    padding: 0 1rem;\n    box-sizing: border-box;\n  }\n  \n  .card {\n    background: var(--card-bg);\n    border-radius: 12px;\n    padding: 2rem;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n    width: 100%;\n    box-sizing: border-box;\n  }\n  \n  .card h2 {\n    text-align: center;\n    margin-bottom: 2rem;\n    color: var(--text-color);\n  }\n  \n  .form-group {\n    margin-bottom: 1.5rem;\n    width: 100%;\n    box-sizing: border-box;\n  }\n  \n  .form-group label {\n    display: block;\n    margin-bottom: 0.5rem;\n    font-weight: 500;\n    color: var(--text-color);\n  }\n  \n  .form-group input {\n    width: 100%;\n    padding: 0.75rem;\n    border: 1px solid var(--border-color);\n    border-radius: 8px;\n    background: var(--card-bg);\n    color: var(--text-color);\n    font-size: 1rem;\n    outline: none;\n    box-sizing: border-box;\n    transition: border-color 0.2s;\n  }\n  \n  .verify-button {\n    width: 100%;\n    padding: 1rem;\n    margin-top: 1rem;\n    background: var(--primary-color);\n    color: white;\n    border: none;\n    border-radius: 8px;\n    font-size: 1rem;\n    font-weight: 500;\n    cursor: pointer;\n    transition: background-color 0.2s;\n  }\n  \n  .verify-button:hover {\n    background: var(--primary-color-dark);\n  }\n  \n  .error-alert {\n    background-color: var(--error-bg);\n    color: var(--error-color);\n    padding: 0.75rem;\n    border-radius: 8px;\n    margin-bottom: 1rem;\n  }\n  \n  /* Dark mode variables */\n  .dark {\n    --primary-color: #8884d8;\n    --primary-color-dark: #7673be;\n    --card-bg: #2a2a2a;\n    --border-color: #404040;\n    --text-color: #ffffff;\n    --error-bg: #ff000020;\n    --error-color: #ff6b6b;\n  }\n  \n  /* Light mode variables */\n  .light {\n    --primary-color: #8884d8;\n    --primary-color-dark: #7673be;\n    --card-bg: #ffffff;\n    --border-color: #e0e0e0;\n    --text-color: #333333;\n    --error-bg: #ff000010;\n    --error-color: #dc3545;\n  }\n\n  form {\n    width: 100%;\n    box-sizing: border-box;\n  }", ".app {\n  min-height: 100vh;\n  padding: 20px;\n  transition: background-color 0.3s, color 0.3s;\n}\n\n.app.dark {\n  background-color: #1a202c;\n  color: white;\n}\n\n.app.light {\n  background-color: #f7fafc;\n  color: #1a202c;\n}\n\n.container {\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.logo {\n  display: flex;\n  align-items: center;\n}\n\n.logo-circle {\n  width: 40px;\n  height: 40px;\n  background-color: #ecc94b;\n  border-radius: 50%;\n  margin-right: 10px;\n}\n\n.theme-toggle {\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: 5px;\n  border-radius: 50%;\n}\n\n.dark .theme-toggle {\n  background-color: #2d3748;\n}\n\n.light .theme-toggle {\n  background-color: #e2e8f0;\n}\n\n.tabs {\n  display: flex;\n  background-color: #2d3748;\n  border-radius: 8px;\n  overflow: hidden;\n  margin-bottom: 20px;\n}\n\n.tab-button {\n  padding: 10px 20px;\n  border: none;\n  background: none;\n  color: #a0aec0;\n  cursor: pointer;\n  transition: background-color 0.3s;\n}\n\n.tab-button.active {\n  background-color: #ecc94b;\n  color: #1a202c;\n}\n\n.dashboard-grid,\n.insights-grid,\n.inventory-content,\n.customers-content,\n.sales-content,\n.orders-content {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n}\n\n.card {\n  background-color: #2d3748;\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n}\n\n.light .card {\n  background-color: white;\n}\n\n.card h3 {\n  margin-top: 0;\n  margin-bottom: 10px;\n}\n\n.card-value {\n  font-size: 24px;\n  font-weight: bold;\n  color: #4299e1;\n  margin: 10px 0;\n}\n\n.card-subtitle {\n  color: #a0aec0;\n  font-size: 14px;\n}\n\n.full-width {\n  grid-column: 1 / -1;\n}\n\n.alert,\n.quick-action {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.action-button {\n  background-color: #ecc94b;\n  color: #1a202c;\n  border: none;\n  padding: 5px 10px;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\n.quick-actions-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 10px;\n}\n\n.inventory-table,\n.customer-table,\n.order-table {\n  width: 100%;\n  border-collapse: collapse;\n  margin-top: 10px;\n}\n\n.inventory-table th,\n.inventory-table td,\n.customer-table th,\n.customer-table td,\n.order-table th,\n.order-table td {\n  border: 1px solid #4a5568;\n  padding: 8px;\n  text-align: left;\n}\n\n.inventory-table th,\n.customer-table th,\n.order-table th {\n  background-color: #2d3748;\n  color: #ecc94b;\n}\n\n.light .inventory-table th,\n.light .customer-table th,\n.light .order-table th {\n  background-color: #edf2f7;\n  color: #2d3748;\n}\n\n.notifications-container {\n  position: fixed;\n  top: 20px;\n  right: 20px;\n  z-index: 1000;\n}\n\n.notification {\n  background-color: #4a5568;\n  color: white;\n  padding: 10px 15px;\n  border-radius: 4px;\n  margin-bottom: 10px;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  animation: slideIn 0.3s ease-out;\n  cursor: pointer;\n}\n\n.light .notification {\n  background-color: #e2e8f0;\n  color: #2d3748;\n}\n\n.notification .close-button {\n  background: none;\n  border: none;\n  color: inherit;\n  cursor: pointer;\n  padding: 0;\n  margin-left: 10px;\n}\n\n@keyframes slideIn {\n  from {\n    transform: translateX(100%);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0);\n    opacity: 1;\n  }\n}\n\n@media (max-width: 768px) {\n  .dashboard-grid,\n  .insights-grid,\n  .inventory-content,\n  .customers-content,\n  .sales-content,\n  .orders-content {\n    grid-template-columns: 1fr;\n  }\n\n  .quick-actions-grid {\n    grid-template-columns: 1fr;\n  }\n\n  .tabs {\n    flex-wrap: wrap;\n  }\n\n  .tab-button {\n    flex-grow: 1;\n    text-align: center;\n  }\n}\n\n.table-container {\n  max-height: 500px;\n  overflow-y: auto;\n  overflow-x: auto;\n}\n\n.events-table {\n  width: 100%;\n  border-collapse: collapse;\n}\n\n.events-table th {\n  position: sticky;\n  top: 0;\n  background-color: var(--background-color);\n  z-index: 1;\n}\n\n.events-table th,\n.events-table td {\n  padding: 8px;\n  text-align: left;\n  border: 1px solid var(--border-color);\n}\n\n.events-table td {\n  white-space: pre-wrap;\n  word-break: break-word;\n}\n\n.eye-button {\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: 4px;\n  border-radius: 4px;\n  transition: background-color 0.2s;\n}\n\n.eye-button:hover {\n  background-color: rgba(0, 0, 0, 0.1);\n}\n\n.dark .eye-button {\n  color: white;\n}\n\n.dark .eye-button:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n\n/* Detail View Styles */\n.detail-view {\n  width: 100%;\n  height: 100%;\n}\n\n.detail-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 24px;\n  gap: 20px;\n  padding: 0 16px;\n}\n\n.detail-header h2 {\n  margin: 0;\n  font-size: 1.5rem;\n}\n\n.back-button {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 10px 20px;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  font-size: 16px;\n  font-weight: 500;\n  border: none;\n}\n\n/* Light Mode Styles */\n.light .back-button {\n  background: #e0e7ff;\n  color: #4f46e5;\n}\n\n.light .back-button:hover {\n  background: #c7d2fe;\n  transform: translateX(-3px);\n}\n\n.light .detail-header h2 {\n  color: #1f2937;\n}\n\n.light .detail-table {\n  background: #ffffff;\n  border: 1px solid #e5e7eb;\n}\n\n.light .key-cell {\n  background-color: #f3f4f6;\n  color: #4b5563;\n}\n\n.light .value-cell {\n  color: #1f2937;\n}\n\n.light .array-item {\n  background: #f9fafb;\n  border: 1px solid #e5e7eb;\n}\n\n.light .sub-key {\n  color: #4b5563;\n}\n\n.light .sub-value {\n  color: #1f2937;\n}\n\n/* Dark Mode Styles */\n.dark .back-button {\n  background: #312e81;\n  color: #e0e7ff;\n}\n\n.dark .back-button:hover {\n  background: #3730a3;\n  transform: translateX(-3px);\n}\n\n.dark .detail-header h2 {\n  color: #f3f4f6;\n}\n\n.dark .detail-table {\n  background: #1f2937;\n  border: 1px solid #374151;\n}\n\n.dark .key-cell {\n  background-color: #111827;\n  color: #9ca3af;\n  border-bottom: 1px solid #374151;\n}\n\n.dark .value-cell {\n  color: #e5e7eb;\n  border-bottom: 1px solid #374151;\n}\n\n.dark .array-item {\n  background: #111827;\n  border: 1px solid #374151;\n}\n\n.dark .sub-key {\n  color: #9ca3af;\n}\n\n.dark .sub-value {\n  color: #e5e7eb;\n}\n\n/* Common Styles */\n.detail-table-wrapper {\n  padding: 0 16px;\n}\n\n.detail-table {\n  width: 100%;\n  border-collapse: separate;\n  border-spacing: 0;\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n.key-cell {\n  width: 25%;\n  padding: 16px;\n  font-weight: 600;\n}\n\n.value-cell {\n  width: 75%;\n  padding: 16px;\n}\n\n.array-value,\n.object-value {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.array-item {\n  padding: 12px;\n  border-radius: 6px;\n  margin: 4px 0;\n}\n\n.sub-item {\n  display: flex;\n  gap: 12px;\n  padding: 4px 0;\n}\n\n.sub-key {\n  font-weight: 500;\n  min-width: 120px;\n}\n\n/* Hover Effects */\n.detail-table tr:hover .key-cell {\n  background-color: #374151;\n  color: #e5e7eb;\n}\n\n.dark .detail-table tr:hover {\n  background-color: #374151;\n}\n\n.light .detail-table tr:hover {\n  background-color: #f9fafb;\n}\n\n/* Transitions */\n.back-button,\n.detail-table tr {\n  transition: all 0.2s ease-in-out;\n}\n\n.empty-notifications,\n.empty-table {\n  text-align: center;\n  padding: 20px;\n  color: #666;\n  font-style: italic;\n}\n\n.empty-table {\n  background: rgba(0, 0, 0, 0.05);\n  border-radius: 8px;\n  margin: 20px 0;\n}\n\n.header-actions {\n  display: flex;\n  gap: 10px;\n  align-items: center;\n}\n\n.logout-button {\n  background: none;\n  border: none;\n  cursor: pointer;\n  color: inherit;\n  padding: 5px;\n  border-radius: 5px;\n  transition: background-color 0.2s;\n}\n\n.logout-button:hover {\n  background-color: rgba(0, 0, 0, 0.1);\n}\n\n.dark .logout-button:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n\n\n\n\n/* Notification Permission Modal */\n.notification-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 10000;\n  animation: fadeIn 0.3s ease-out;\n}\n\n.notification-modal {\n  background: white;\n  border-radius: 16px;\n  padding: 0;\n  max-width: 480px;\n  width: 90%;\n  max-height: 80vh;\n  overflow: hidden;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n  animation: slideInUp 0.3s ease-out;\n  position: relative;\n}\n\n.dark .notification-modal {\n  background: #2d3748;\n  color: white;\n}\n\n.notification-modal-header {\n  position: relative;\n  padding: 16px 20px 0 20px;\n}\n\n.modal-close-button {\n  position: absolute;\n  top: 16px;\n  right: 16px;\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: 8px;\n  border-radius: 50%;\n  color: #718096;\n  transition: all 0.2s ease;\n}\n\n.modal-close-button:hover {\n  background: rgba(0, 0, 0, 0.1);\n  color: #2d3748;\n}\n\n.dark .modal-close-button {\n  color: #a0aec0;\n}\n\n.dark .modal-close-button:hover {\n  background: rgba(255, 255, 255, 0.1);\n  color: white;\n}\n\n.notification-modal-content {\n  padding: 20px 32px;\n  text-align: center;\n}\n\n.modal-icon {\n  margin-bottom: 16px;\n  color: #3182ce;\n}\n\n.modal-icon.denied {\n  color: #e53e3e;\n}\n\n.modal-icon.default {\n  color: #38a169;\n  animation: pulse 2s infinite;\n}\n\n.notification-modal-content h2 {\n  margin: 0 0 12px 0;\n  font-size: 24px;\n  font-weight: 600;\n  color: #2d3748;\n}\n\n.dark .notification-modal-content h2 {\n  color: white;\n}\n\n.notification-modal-content p {\n  margin: 0 0 20px 0;\n  color: #4a5568;\n  line-height: 1.6;\n  font-size: 16px;\n}\n\n.dark .notification-modal-content p {\n  color: #cbd5e0;\n}\n\n.notification-instructions {\n  background: #f7fafc;\n  border-radius: 8px;\n  padding: 16px;\n  margin: 20px 0;\n  text-align: left;\n}\n\n.dark .notification-instructions {\n  background: #4a5568;\n}\n\n.instruction-item {\n  margin: 8px 0;\n  color: #2d3748;\n  font-size: 14px;\n  line-height: 1.5;\n}\n\n.dark .instruction-item {\n  color: #e2e8f0;\n}\n\n.notification-modal-actions {\n  padding: 20px 32px 32px 32px;\n  display: flex;\n  gap: 12px;\n  justify-content: center;\n  flex-wrap: wrap;\n}\n\n.modal-primary-button, .modal-secondary-button {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 12px 24px;\n  border: none;\n  border-radius: 8px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  font-size: 14px;\n  min-width: 120px;\n  justify-content: center;\n}\n\n.modal-primary-button {\n  background: #3182ce;\n  color: white;\n}\n\n.modal-primary-button:hover {\n  background: #2c5aa0;\n  transform: translateY(-1px);\n}\n\n.modal-secondary-button {\n  background: #e2e8f0;\n  color: #4a5568;\n}\n\n.modal-secondary-button:hover {\n  background: #cbd5e0;\n}\n\n.dark .modal-secondary-button {\n  background: #4a5568;\n  color: #e2e8f0;\n}\n\n.dark .modal-secondary-button:hover {\n  background: #718096;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes slideInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px) scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n@keyframes pulse {\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.1);\n  }\n}\n", ".react-tabs {\n  -webkit-tap-highlight-color: transparent;\n}\n\n.react-tabs__tab-list {\n  border-bottom: 1px solid #aaa;\n  margin: 0 0 10px;\n  padding: 0;\n}\n\n.react-tabs__tab {\n  display: inline-block;\n  border: 1px solid transparent;\n  border-bottom: none;\n  bottom: -1px;\n  position: relative;\n  list-style: none;\n  padding: 6px 12px;\n  cursor: pointer;\n}\n\n.react-tabs__tab--selected {\n  background: #fff;\n  border-color: #aaa;\n  color: black;\n  border-radius: 5px 5px 0 0;\n}\n\n.react-tabs__tab--disabled {\n  color: GrayText;\n  cursor: default;\n}\n\n.react-tabs__tab:focus {\n  outline: none;\n}\n\n.react-tabs__tab:focus:after {\n  content: '';\n  position: absolute;\n  height: 5px;\n  left: -4px;\n  right: -4px;\n  bottom: -5px;\n  background: #fff;\n}\n\n.react-tabs__tab-panel {\n  display: none;\n}\n\n.react-tabs__tab-panel--selected {\n  display: block;\n}\n", ".App {\n  text-align: center;\n}\n\n.App-logo {\n  height: 40vmin;\n  pointer-events: none;\n}\n\n@media (prefers-reduced-motion: no-preference) {\n  .App-logo {\n    animation: App-logo-spin infinite 20s linear;\n  }\n}\n\n.App-header {\n  background-color: #282c34;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  font-size: calc(10px + 2vmin);\n  color: white;\n}\n\n.App-link {\n  color: #61dafb;\n}\n\n@keyframes App-logo-spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n"], "names": [], "sourceRoot": ""}