{"version": 3, "file": "static/css/main.55b78cff.css", "mappings": "AAAA,KAKE,kCAAmC,CACnC,iCAAkC,CAJlC,mIAEY,CAHZ,QAMF,CAEA,KACE,uEAEF,CCZA,iBAGI,kBAAmB,CAEnB,wBAAyB,CAJzB,YAAa,CACb,sBAAuB,CAEvB,gBAEF,CAEA,WACE,qBAAuB,CAEvB,iBAAkB,CAClB,8BAAwC,CAExC,eAAgB,CAJhB,YAAa,CAGb,UAEF,CAEA,cAGE,aAAc,CADd,kBAAmB,CADnB,iBAGF,CAEA,eAEE,gBAAiB,CADjB,eAAgB,CAEhB,cAEF,CAEA,qBAHE,qBAUF,CAPA,MACE,yBAA0B,CAG1B,8BAAwC,CADxC,YAAa,CAEb,UAEF,CAEA,SAGE,uBAAwB,CADxB,kBAAmB,CADnB,iBAGF,CAEA,YAGE,qBAAsB,CAFtB,oBAAqB,CACrB,UAEF,CAEA,kBAIE,uBAAwB,CAHxB,aAAc,CAEd,eAAgB,CADhB,mBAGF,CAEA,kBAKE,yBAA0B,CAF1B,oCAAqC,CACrC,iBAAkB,CAKlB,qBAAsB,CAHtB,uBAAwB,CACxB,cAAe,CACf,YAAa,CANb,cAAgB,CAQhB,2BAA6B,CAT7B,UAUF,CAEA,eAIE,+BAAgC,CAEhC,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAKZ,cAAe,CAFf,cAAe,CACf,eAAgB,CANhB,eAAgB,CADhB,YAAa,CASb,+BAAiC,CAVjC,UAWF,CAEA,qBACE,oCACF,CAEA,aACE,gCAAiC,CAGjC,iBAAkB,CAFlB,wBAAyB,CAGzB,kBAAmB,CAFnB,cAGF,CAGA,MACE,uBAAwB,CACxB,4BAA6B,CAC7B,iBAAkB,CAClB,sBAAuB,CACvB,iBAAqB,CACrB,oBAAqB,CACrB,qBACF,CAGA,OACE,uBAAwB,CACxB,4BAA6B,CAC7B,cAAkB,CAClB,sBAAuB,CACvB,iBAAqB,CACrB,oBAAqB,CACrB,qBACF,CAEA,KAEE,qBAAsB,CADtB,UAEF,CC1HF,KAIE,qGAAkH,CAClH,eAAgB,CAChB,eAAgB,CALhB,gBAAiB,CACjB,SAAU,CACV,0CAIF,CAEA,UACE,kBAAmB,CACnB,8HAEiE,CACjE,UACF,CAEA,WACE,kBAAmB,CACnB,8HAEiE,CACjE,aACF,CAEA,WAEE,aAAc,CADd,gBAAiB,CAEjB,YACF,CAEA,QAGE,kBAAmB,CAInB,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAqC,CAGrC,0BAA2C,CAD3C,kBAAmB,CAEnB,0EAGwC,CAZxC,YAAa,CACb,6BAA8B,CAE9B,kBAAmB,CACnB,iBAAkB,CASlB,iBACF,CAEA,cACE,oBAAiC,CACjC,0BAA2C,CAC3C,sEAIF,CAEA,eACE,gBAAoC,CACpC,0BAAqC,CACrC,sEAIF,CAEA,MAGE,QACF,CAEA,mBAJE,kBAAmB,CADnB,YAiBF,CAZA,aAGE,kDAA6D,CAC7D,kBAAmB,CAInB,kDAEwC,CARxC,WAAY,CAKZ,sBAAuB,CAIvB,iBAAkB,CAVlB,UAWF,CAEA,oBACE,YAAa,CAEb,sBAAuB,CADvB,cAEF,CAEA,SAIE,aAAc,CAHd,cAAe,CACf,eAAgB,CAGhB,sBAAwB,CAFxB,QAGF,CAEA,eACE,UACF,CAEA,gBACE,aACF,CAEA,gBAEE,OAEF,CAEA,6BAQE,kBAAmB,CAPnB,gBAAuB,CACvB,0BAA0C,CAG1C,iBAAkB,CAKlB,aAAc,CAPd,cAAe,CAIf,YAAa,CAKb,WAAY,CAHZ,sBAAuB,CALvB,YAAa,CAEb,0CAAiD,CAKjD,UAEF,CAEA,yCACE,oBAAqC,CACrC,kBAAsC,CACtC,0BACF,CAEA,yCACE,0BAA0C,CAC1C,aACF,CAEA,qDACE,oBAAqC,CACrC,kBACF,CAEA,2CACE,0BAAoC,CACpC,aACF,CAEA,uDACE,oBAA+B,CAC/B,sBACF,CAEA,eAEE,sBAAoC,CADpC,aAEF,CAEA,qBACE,oBAAkC,CAClC,sBACF,CAEA,MAEE,wBAAyB,CACzB,iBAAkB,CAFlB,YAAa,CAIb,kBAAmB,CADnB,eAEF,CAEA,YAGE,eAAgB,CADhB,WAAY,CAEZ,aAAc,CACd,cAAe,CAJf,iBAAkB,CAKlB,+BACF,CAEA,mBACE,wBAAyB,CACzB,aACF,CAEA,oGAQE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,wDAEF,CAEA,MAEE,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAqC,CAIrC,0BAA2C,CAF3C,kBAAmB,CAGnB,kDAE+B,CAJ/B,YAAa,CAKb,0CACF,CAEA,YAEE,kDAE+B,CAH/B,0BAIF,CAEA,YACE,oBAAiC,CACjC,0BAA2C,CAC3C,8CAGF,CAEA,aACE,oBAAoC,CACpC,0BAAqC,CACrC,kDAGF,CAEA,SAEE,kBAAmB,CADnB,YAEF,CAEA,YAGE,aAAc,CAFd,cAAe,CACf,eAAiB,CAEjB,aACF,CAEA,eACE,aAAc,CACd,cACF,CAEA,YACE,gBACF,CAEA,qBAIE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,eACE,wBAAyB,CAEzB,WAAY,CAEZ,iBAAkB,CAHlB,aAAc,CAId,cAAe,CAFf,gBAGF,CAEA,oBAGE,aAAS,CAFT,YAAa,CAEb,QAAS,CADT,6BAEF,CAEA,8CAIE,wBAAyB,CACzB,eAAgB,CAFhB,UAGF,CAEA,8GAME,wBAAyB,CACzB,WAAY,CACZ,eACF,CAEA,uDAGE,wBAAyB,CACzB,aACF,CAEA,4EAGE,wBAAyB,CACzB,aACF,CAEA,yBAKE,eAAgB,CAJhB,cAAe,CAEf,UAAW,CADX,QAAS,CAET,YAEF,CAEA,cASE,sBAAuB,CAKvB,kDAAyD,CAZzD,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAqC,CASrC,0BAAqC,CALrC,iBAAkB,CAMlB,kDAE+B,CAV/B,aAAc,CAYd,cAAe,CARf,YAAa,CACb,6BAA8B,CAF9B,iBAAkB,CAFlB,iBAAkB,CAalB,iBAAkB,CADlB,uBAEF,CAEA,qBAOE,kBAAmB,CAFnB,QAAS,CAJT,UAAW,CAEX,MAAO,CADP,iBAAkB,CAElB,KAAM,CAEN,SAEF,CAEA,oBAEE,kDAE+B,CAH/B,0BAIF,CAEA,oBACE,oBAAkC,CAClC,0BAA2C,CAE3C,8CAE8B,CAH9B,UAIF,CAEA,qBACE,oBAAqC,CACrC,0BAAqC,CACrC,aACF,CAEA,4BAUE,kBAAmB,CATnB,gBAAuB,CACvB,WAAY,CAKZ,iBAAkB,CAJlB,aAAc,CACd,cAAe,CAKf,YAAa,CAKb,aAAc,CADd,WAAY,CAFZ,sBAAuB,CALvB,gBAAiB,CADjB,WAAY,CAGZ,wBAA0B,CAI1B,UAGF,CAEA,kCACE,oBAAoC,CACpC,aACF,CAEA,kCACE,aACF,CAEA,wCACE,oBAAoC,CACpC,aACF,CAEA,sBAEE,YAAa,CADb,QAAO,CAEP,qBAAsB,CACtB,OACF,CAEA,sBAEE,cAAe,CADf,eAAgB,CAEhB,eAAgB,CAChB,QACF,CAEA,mBACE,cAAe,CAEf,eAAgB,CADhB,UAEF,CAEA,wBACE,GAEE,SAAU,CADV,oCAEF,CACA,GAEE,SAAU,CADV,gCAEF,CACF,CAcA,6EAGE,UAAW,CADX,SAEF,CAEA,yFAEE,gBACF,CAEA,yFAEE,oBAAoC,CACpC,iBACF,CAEA,qGAEE,oBACF,CAEA,qGAEE,oBACF,CAEA,iHAEE,oBACF,CAGA,oBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAYA,0BACE,WACE,YACF,CAEA,QAEE,kBAAmB,CADnB,iBAEF,CAEA,MACE,YACF,CACF,CAEA,yBACE,WACE,YACF,CAEA,QACE,kBAAmB,CACnB,QAAS,CACT,YAAa,CACb,iBACF,CAEA,gBACE,sBACF,CAEA,SACE,cACF,CAEA,MAEE,kBAAmB,CADnB,YAEF,CAEA,kCAGE,cAAe,CADf,iBAEF,CAEA,eAEE,kBAAmB,CADnB,kBAAmB,CAEnB,QACF,CAEA,kBACE,cACF,CAEA,yBACE,SAAU,CAGV,cAAe,CAFf,UAAW,CACX,QAEF,CAEA,cACE,iBACF,CACF,CAEA,yBACE,UAGE,cAAe,CADf,YAAa,CADb,SAGF,CAEA,YAGE,cAAe,CADf,YAAa,CADb,SAGF,CAEA,aAEE,cAAe,CADf,iBAEF,CAEA,uDACE,YACF,CACF,CAEA,iBAKE,gBAAuB,CACvB,0BAA2C,CAF3C,iBAAkB,CAHlB,gBAAiB,CAEjB,eAAgB,CADhB,eAKF,CAEA,uBACE,0BACF,CAEA,wBACE,0BACF,CAEA,cAEE,uBAAyB,CACzB,gBAAiB,CACjB,cAAe,CAHf,UAIF,CAEA,iBAGE,kBAAmB,CASnB,WAA4C,CAA5C,iCAA4C,CAR5C,aAAc,CAMd,cAAe,CAHf,eAAgB,CAEhB,qBAAuB,CAHvB,iBAAkB,CALlB,eAAgB,CAOhB,wBAAyB,CANzB,KAAM,CAGN,UAQF,CAEA,uBACE,kBAAmB,CAEnB,iCAAkD,CADlD,aAEF,CAEA,iBAGE,WAA4C,CAA5C,iCAA4C,CAI5C,cAAe,CACf,eAAgB,CAPhB,iBAAkB,CAKlB,qCAAuC,CAFvC,oBAAqB,CACrB,qBAIF,CAEA,6BACE,oBACF,CAEA,uBACE,iCAAkD,CAClD,aACF,CAEA,wBACE,iCAA4C,CAC5C,aACF,CAEA,mCACE,oBACF,CAEA,oCACE,oBACF,CAEA,YASE,kBAAmB,CARnB,kBAAmB,CACnB,WAAY,CAGZ,iBAAkB,CAMlB,8BAAwC,CAJxC,UAAY,CAJZ,cAAe,CAKf,YAAa,CAKb,WAAY,CAHZ,sBAAuB,CANvB,WAAY,CAEZ,wBAA0B,CAM1B,UAEF,CAEA,kBACE,kBAAmB,CAEnB,8BAAyC,CADzC,0BAEF,CAEA,mBAEE,8BAAwC,CADxC,uBAEF,CAGA,aAGE,8CAAqD,CADrD,WAAY,CADZ,UAGF,CAEA,eAEE,kBAAmB,CADnB,YAAa,CAGb,QAAS,CADT,kBAAmB,CAEnB,aACF,CAEA,kBAIE,aAAc,CAFd,cAAe,CACf,eAAgB,CAEhB,sBAAwB,CAJxB,QAKF,CAEA,wBACE,UACF,CAEA,aAEE,kBAAmB,CASnB,kBAAmB,CADnB,0BAAwC,CALxC,iBAAkB,CAQlB,8BAAwC,CADxC,UAAY,CANZ,cAAe,CALf,YAAa,CAOb,cAAe,CACf,eAAgB,CANhB,OAAQ,CACR,gBAAiB,CAGjB,uBAOF,CAEA,mBACE,kBAAmB,CAEnB,8BAAyC,CADzC,0BAEF,CAEA,oBAEE,8BAAwC,CADxC,uBAEF,CAGA,oBACE,kBAAmB,CACnB,aACF,CAEA,0BACE,kBAAmB,CACnB,0BACF,CAEA,yBACE,aACF,CAEA,qBACE,eAAmB,CACnB,wBACF,CAEA,iBACE,wBAAyB,CACzB,aACF,CAEA,mBACE,aACF,CAEA,mBACE,kBAAmB,CACnB,wBACF,CAEA,gBACE,aACF,CAEA,kBACE,aACF,CAGA,mBACE,kBAAmB,CACnB,aACF,CAEA,yBACE,kBAAmB,CACnB,0BACF,CAEA,wBACE,aACF,CAEA,oBACE,kBAAmB,CACnB,wBACF,CAEA,gBACE,wBAAyB,CAEzB,+BAAgC,CADhC,aAEF,CAEA,kBAEE,+BAAgC,CADhC,aAEF,CAEA,kBACE,kBAAmB,CACnB,wBACF,CAEA,eACE,aACF,CAEA,iBACE,aACF,CAGA,sBAIE,gBAAuB,CACvB,0BAA2C,CAH3C,iBAAkB,CAClB,eAAgB,CAFhB,aAKF,CAEA,4BACE,0BACF,CAEA,6BACE,0BACF,CAEA,cAEE,uBAAyB,CAEzB,iBAAkB,CADlB,gBAAiB,CAEjB,eAAgB,CAJhB,UAKF,CAEA,UAOE,kBAAmB,CAGnB,WAA4C,CAA5C,iCAA4C,CAF5C,aAAc,CAJd,cAAe,CADf,eAAgB,CAGhB,qBAAuB,CAJvB,iBAAkB,CAGlB,wBAAyB,CAJzB,SAUF,CAEA,gBACE,kBAAmB,CAEnB,iCAAkD,CADlD,aAEF,CAEA,YAOE,oBAAoC,CADpC,WAA4C,CAA5C,iCAA4C,CAE5C,aAAc,CALd,cAAe,CACf,eAAgB,CAFhB,iBAAkB,CADlB,SAQF,CAEA,kBACE,oBAAiC,CAEjC,iCAAkD,CADlD,aAEF,CAEA,mBACE,oBAAoC,CAEpC,iCAA4C,CAD5C,aAEF,CAEA,2BAEE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,YAIE,oBAAmC,CACnC,0BAAyC,CAHzC,kBAAmB,CACnB,YAAa,CAFb,iBAAkB,CAKlB,uBACF,CAEA,kBACE,oBAAoC,CACpC,yBACF,CAEA,UAIE,sBAAuB,CAHvB,YAAa,CACb,QAAS,CACT,aAEF,CAEA,SAGE,aAAc,CACd,cAAe,CAHf,eAAgB,CAKhB,mBAAqB,CAJrB,eAAgB,CAGhB,wBAEF,CAEA,WACE,QAAO,CACP,qBACF,CAGA,iCACE,wBAAyB,CACzB,aACF,CAEA,6BACE,wBACF,CAEA,8BACE,wBACF,CAGA,8BAEE,8BACF,CAEA,kCAQE,kBAAmB,CAJnB,aAAc,CAEd,YAAa,CACb,qBAAsB,CAFtB,cAAe,CAIf,OAAQ,CANR,iBAAkB,CADlB,iBAQF,CAEA,aACE,oBAAqC,CAGrC,2BAA2C,CAF3C,iBAAkB,CAClB,aAEF,CAEA,oBACE,YAKF,CAEA,gDALE,aAAc,CADd,cAAe,CAEf,iBAAkB,CAClB,UASF,CANA,4BACE,YAKF,CAEA,8CAGE,oBAAqC,CACrC,sBAAsC,CAFtC,aAGF,CAEA,gDAEE,aACF,CAEA,gBAGE,kBAAmB,CAFnB,YAAa,CACb,QAEF,CAEA,eACE,eAAgB,CAChB,WAAY,CAIZ,iBAAkB,CAFlB,aAAc,CADd,cAAe,CAEf,WAAY,CAEZ,+BACF,CAEA,qBACE,0BACF,CAEA,2BACE,0BACF,CAGA,4BAQE,kBAAmB,CAGnB,6BAA+B,CAL/B,oBAA8B,CAD9B,QAAS,CAET,YAAa,CAEb,sBAAuB,CANvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,aAEF,CAEA,oBASE,gCAAkC,CARlC,eAAiB,CACjB,kBAAmB,CAMnB,gCAA0C,CAF1C,eAAgB,CAFhB,eAAgB,CAGhB,eAAgB,CAJhB,SAAU,CAOV,iBAAkB,CALlB,SAMF,CAEA,0BACE,kBAAmB,CACnB,UACF,CAEA,2BAEE,mBAAyB,CADzB,iBAEF,CAEA,oBAIE,eAAgB,CAChB,WAAY,CAGZ,iBAAkB,CAClB,aAAc,CAHd,cAAe,CACf,WAAY,CANZ,iBAAkB,CAElB,UAAW,CADX,QAAS,CAQT,uBACF,CAEA,0BACE,oBAA8B,CAC9B,aACF,CAEA,0BACE,aACF,CAEA,gCACE,oBAAoC,CACpC,UACF,CAEA,4BACE,iBAAkB,CAClB,iBACF,CAEA,YAEE,aAAc,CADd,kBAEF,CAEA,mBACE,aACF,CAEA,oBAEE,2BAA4B,CAD5B,aAEF,CAEA,+BAIE,aAAc,CAFd,cAAe,CACf,eAAgB,CAFhB,eAIF,CAEA,qCACE,UACF,CAEA,8BAEE,aAAc,CAEd,cAAe,CADf,eAAgB,CAFhB,eAIF,CAEA,oCACE,aACF,CAEA,2BACE,kBAAmB,CACnB,iBAAkB,CAElB,aAAc,CADd,YAAa,CAEb,eACF,CAEA,iCACE,kBACF,CAEA,kBAEE,aAAc,CACd,cAAe,CACf,eAAgB,CAHhB,YAIF,CAEA,wBACE,aACF,CAEA,4BAEE,YAAa,CAGb,cAAe,CAFf,QAAS,CACT,sBAAuB,CAHvB,sBAKF,CAEA,8CAEE,kBAAmB,CAGnB,WAAY,CACZ,iBAAkB,CAElB,cAAe,CAPf,YAAa,CASb,cAAe,CAHf,eAAgB,CAJhB,OAAQ,CASR,sBAAuB,CADvB,eAAgB,CAPhB,iBAAkB,CAKlB,uBAIF,CAEA,sBACE,kBAAmB,CACnB,UACF,CAEA,4BACE,kBAAmB,CACnB,0BACF,CAEA,wBACE,kBAAmB,CACnB,aACF,CAEA,8BACE,kBACF,CAEA,8BACE,kBAAmB,CACnB,aACF,CAEA,oCACE,kBACF,CAEA,kBACE,GACE,SACF,CACA,GACE,SACF,CACF,CAEA,qBACE,GACE,SAAU,CACV,qCACF,CACA,GACE,SAAU,CACV,gCACF,CACF,CAEA,iBACE,MACE,kBACF,CACA,IACE,oBACF,CACF,CCztCA,YACE,uCACF,CAEA,sBACE,4BAA6B,CAC7B,eAAgB,CAChB,SACF,CAEA,iBAGE,sBAAmB,CAAnB,kBAAmB,CACnB,WAAY,CAIZ,cAAe,CAPf,oBAAqB,CAKrB,eAAgB,CAChB,gBAAiB,CAFjB,iBAIF,CAEA,2BACE,eAAgB,CAChB,iBAAkB,CAElB,yBAA0B,CAD1B,UAEF,CAEA,2BACE,cAAe,CACf,cACF,CAEA,uBACE,YACF,CAEA,6BAOE,eAAgB,CADhB,WAAY,CALZ,UAAW,CAEX,UAAW,CACX,SAAU,CAFV,iBAAkB,CAGlB,UAGF,CAEA,uBACE,YACF,CAEA,iCACE,aACF,CCrDA,KACE,iBACF,CAEA,UACE,aAAc,CACd,mBACF,CAEA,8CACE,UACE,2CACF,CACF,CAEA,YAKE,kBAAmB,CAJnB,wBAAyB,CAOzB,UAAY,CALZ,YAAa,CACb,qBAAsB,CAGtB,4BAA6B,CAD7B,sBAAuB,CAJvB,gBAOF,CAEA,UACE,aACF,CAEA,yBACE,GACE,sBACF,CACA,GACE,uBACF,CACF", "sources": ["index.css", "components/Login.css", "components/Dashboard.css", "../node_modules/react-tabs/style/react-tabs.css", "App.css"], "sourcesContent": ["body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n", ".login-container {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    min-height: 100vh;\n    background-color: #f7fafc;\n  }\n  \n  .login-box {\n    background-color: white;\n    padding: 2rem;\n    border-radius: 8px;\n    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\n    width: 100%;\n    max-width: 400px;\n  }\n  \n  .login-box h2 {\n    text-align: center;\n    margin-bottom: 2rem;\n    color: #2d3748;\n  }\n  \n  .login-content {\n    max-width: 500px;\n    margin: 4rem auto;\n    padding: 0 1rem;\n    box-sizing: border-box;\n  }\n  \n  .card {\n    background: var(--card-bg);\n    border-radius: 12px;\n    padding: 2rem;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n    width: 100%;\n    box-sizing: border-box;\n  }\n  \n  .card h2 {\n    text-align: center;\n    margin-bottom: 2rem;\n    color: var(--text-color);\n  }\n  \n  .form-group {\n    margin-bottom: 1.5rem;\n    width: 100%;\n    box-sizing: border-box;\n  }\n  \n  .form-group label {\n    display: block;\n    margin-bottom: 0.5rem;\n    font-weight: 500;\n    color: var(--text-color);\n  }\n  \n  .form-group input {\n    width: 100%;\n    padding: 0.75rem;\n    border: 1px solid var(--border-color);\n    border-radius: 8px;\n    background: var(--card-bg);\n    color: var(--text-color);\n    font-size: 1rem;\n    outline: none;\n    box-sizing: border-box;\n    transition: border-color 0.2s;\n  }\n  \n  .verify-button {\n    width: 100%;\n    padding: 1rem;\n    margin-top: 1rem;\n    background: var(--primary-color);\n    color: white;\n    border: none;\n    border-radius: 8px;\n    font-size: 1rem;\n    font-weight: 500;\n    cursor: pointer;\n    transition: background-color 0.2s;\n  }\n  \n  .verify-button:hover {\n    background: var(--primary-color-dark);\n  }\n  \n  .error-alert {\n    background-color: var(--error-bg);\n    color: var(--error-color);\n    padding: 0.75rem;\n    border-radius: 8px;\n    margin-bottom: 1rem;\n  }\n  \n  /* Dark mode variables */\n  .dark {\n    --primary-color: #8884d8;\n    --primary-color-dark: #7673be;\n    --card-bg: #2a2a2a;\n    --border-color: #404040;\n    --text-color: #ffffff;\n    --error-bg: #ff000020;\n    --error-color: #ff6b6b;\n  }\n  \n  /* Light mode variables */\n  .light {\n    --primary-color: #8884d8;\n    --primary-color-dark: #7673be;\n    --card-bg: #ffffff;\n    --border-color: #e0e0e0;\n    --text-color: #333333;\n    --error-bg: #ff000010;\n    --error-color: #dc3545;\n  }\n\n  form {\n    width: 100%;\n    box-sizing: border-box;\n  }", ".app {\n  min-height: 100vh;\n  padding: 0;\n  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;\n  font-weight: 400;\n  line-height: 1.6;\n}\n\n.app.dark {\n  background: #0a0a0b;\n  background-image:\n    radial-gradient(circle at 25% 25%, #1a1a2e 0%, transparent 50%),\n    radial-gradient(circle at 75% 75%, #16213e 0%, transparent 50%);\n  color: #ffffff;\n}\n\n.app.light {\n  background: #fafbfc;\n  background-image:\n    radial-gradient(circle at 25% 25%, #f0f4f8 0%, transparent 50%),\n    radial-gradient(circle at 75% 75%, #e8f2ff 0%, transparent 50%);\n  color: #1a202c;\n}\n\n.container {\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: 20px;\n}\n\n.header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 24px;\n  padding: 20px 28px;\n  background: rgba(255, 255, 255, 0.02);\n  backdrop-filter: blur(24px);\n  border-radius: 16px;\n  border: 1px solid rgba(255, 255, 255, 0.08);\n  box-shadow:\n    0 1px 3px rgba(0, 0, 0, 0.1),\n    0 1px 2px rgba(0, 0, 0, 0.06),\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\n  position: relative;\n}\n\n.dark .header {\n  background: rgba(26, 32, 44, 0.6);\n  border: 1px solid rgba(255, 255, 255, 0.06);\n  box-shadow:\n    0 4px 6px rgba(0, 0, 0, 0.3),\n    0 1px 3px rgba(0, 0, 0, 0.2),\n    inset 0 1px 0 rgba(255, 255, 255, 0.05);\n}\n\n.light .header {\n  background: rgba(255, 255, 255, 0.8);\n  border: 1px solid rgba(0, 0, 0, 0.06);\n  box-shadow:\n    0 1px 3px rgba(0, 0, 0, 0.1),\n    0 1px 2px rgba(0, 0, 0, 0.06),\n    inset 0 1px 0 rgba(255, 255, 255, 0.8);\n}\n\n.logo {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n}\n\n.logo-circle {\n  width: 40px;\n  height: 40px;\n  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);\n  border-radius: 10px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow:\n    0 2px 4px rgba(37, 99, 235, 0.2),\n    inset 0 1px 0 rgba(255, 255, 255, 0.2);\n  position: relative;\n}\n\n.logo-circle::before {\n  content: '📊';\n  font-size: 18px;\n  filter: brightness(1.2);\n}\n\n.logo h1 {\n  font-size: 22px;\n  font-weight: 600;\n  margin: 0;\n  color: #1a202c;\n  letter-spacing: -0.025em;\n}\n\n.dark .logo h1 {\n  color: #ffffff;\n}\n\n.light .logo h1 {\n  color: #1a202c;\n}\n\n.header-actions {\n  display: flex;\n  gap: 8px;\n  align-items: center;\n}\n\n.theme-toggle, .logout-button {\n  background: transparent;\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  cursor: pointer;\n  padding: 10px;\n  border-radius: 8px;\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: inherit;\n  width: 36px;\n  height: 36px;\n}\n\n.theme-toggle:hover, .logout-button:hover {\n  background: rgba(255, 255, 255, 0.08);\n  border-color: rgba(255, 255, 255, 0.2);\n  transform: translateY(-1px);\n}\n\n.dark .theme-toggle, .dark .logout-button {\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  color: #e2e8f0;\n}\n\n.dark .theme-toggle:hover, .dark .logout-button:hover {\n  background: rgba(255, 255, 255, 0.08);\n  border-color: rgba(255, 255, 255, 0.2);\n}\n\n.light .theme-toggle, .light .logout-button {\n  border: 1px solid rgba(0, 0, 0, 0.1);\n  color: #4a5568;\n}\n\n.light .theme-toggle:hover, .light .logout-button:hover {\n  background: rgba(0, 0, 0, 0.04);\n  border-color: rgba(0, 0, 0, 0.15);\n}\n\n.logout-button {\n  color: #ef4444;\n  border-color: rgba(239, 68, 68, 0.2);\n}\n\n.logout-button:hover {\n  background: rgba(239, 68, 68, 0.1);\n  border-color: rgba(239, 68, 68, 0.3);\n}\n\n.tabs {\n  display: flex;\n  background-color: #2d3748;\n  border-radius: 8px;\n  overflow: hidden;\n  margin-bottom: 20px;\n}\n\n.tab-button {\n  padding: 10px 20px;\n  border: none;\n  background: none;\n  color: #a0aec0;\n  cursor: pointer;\n  transition: background-color 0.3s;\n}\n\n.tab-button.active {\n  background-color: #ecc94b;\n  color: #1a202c;\n}\n\n.dashboard-grid,\n.insights-grid,\n.inventory-content,\n.customers-content,\n.sales-content,\n.orders-content {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: 20px;\n}\n\n.card {\n  background: rgba(255, 255, 255, 0.02);\n  backdrop-filter: blur(24px);\n  border-radius: 12px;\n  padding: 24px;\n  border: 1px solid rgba(255, 255, 255, 0.08);\n  box-shadow:\n    0 1px 3px rgba(0, 0, 0, 0.1),\n    0 1px 2px rgba(0, 0, 0, 0.06);\n  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.card:hover {\n  transform: translateY(-1px);\n  box-shadow:\n    0 4px 6px rgba(0, 0, 0, 0.1),\n    0 2px 4px rgba(0, 0, 0, 0.06);\n}\n\n.dark .card {\n  background: rgba(26, 32, 44, 0.4);\n  border: 1px solid rgba(255, 255, 255, 0.06);\n  box-shadow:\n    0 1px 3px rgba(0, 0, 0, 0.3),\n    0 1px 2px rgba(0, 0, 0, 0.2);\n}\n\n.light .card {\n  background: rgba(255, 255, 255, 0.7);\n  border: 1px solid rgba(0, 0, 0, 0.06);\n  box-shadow:\n    0 1px 3px rgba(0, 0, 0, 0.1),\n    0 1px 2px rgba(0, 0, 0, 0.06);\n}\n\n.card h3 {\n  margin-top: 0;\n  margin-bottom: 10px;\n}\n\n.card-value {\n  font-size: 24px;\n  font-weight: bold;\n  color: #4299e1;\n  margin: 10px 0;\n}\n\n.card-subtitle {\n  color: #a0aec0;\n  font-size: 14px;\n}\n\n.full-width {\n  grid-column: 1 / -1;\n}\n\n.alert,\n.quick-action {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.action-button {\n  background-color: #ecc94b;\n  color: #1a202c;\n  border: none;\n  padding: 5px 10px;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\n.quick-actions-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 10px;\n}\n\n.inventory-table,\n.customer-table,\n.order-table {\n  width: 100%;\n  border-collapse: collapse;\n  margin-top: 10px;\n}\n\n.inventory-table th,\n.inventory-table td,\n.customer-table th,\n.customer-table td,\n.order-table th,\n.order-table td {\n  border: 1px solid #4a5568;\n  padding: 8px;\n  text-align: left;\n}\n\n.inventory-table th,\n.customer-table th,\n.order-table th {\n  background-color: #2d3748;\n  color: #ecc94b;\n}\n\n.light .inventory-table th,\n.light .customer-table th,\n.light .order-table th {\n  background-color: #edf2f7;\n  color: #2d3748;\n}\n\n.notifications-container {\n  position: fixed;\n  top: 16px;\n  right: 16px;\n  z-index: 1000;\n  max-width: 360px;\n}\n\n.notification {\n  background: rgba(255, 255, 255, 0.95);\n  backdrop-filter: blur(16px);\n  color: #1a202c;\n  padding: 16px 20px;\n  border-radius: 8px;\n  margin-bottom: 8px;\n  display: flex;\n  justify-content: space-between;\n  align-items: flex-start;\n  border: 1px solid rgba(0, 0, 0, 0.08);\n  box-shadow:\n    0 4px 6px rgba(0, 0, 0, 0.1),\n    0 1px 3px rgba(0, 0, 0, 0.08);\n  animation: slideInRight 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  cursor: pointer;\n  transition: all 0.2s ease;\n  position: relative;\n}\n\n.notification::before {\n  content: '';\n  position: absolute;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  width: 3px;\n  background: #2563eb;\n}\n\n.notification:hover {\n  transform: translateY(-1px);\n  box-shadow:\n    0 6px 8px rgba(0, 0, 0, 0.12),\n    0 2px 4px rgba(0, 0, 0, 0.08);\n}\n\n.dark .notification {\n  background: rgba(26, 32, 44, 0.95);\n  border: 1px solid rgba(255, 255, 255, 0.08);\n  color: #ffffff;\n  box-shadow:\n    0 4px 6px rgba(0, 0, 0, 0.3),\n    0 1px 3px rgba(0, 0, 0, 0.2);\n}\n\n.light .notification {\n  background: rgba(255, 255, 255, 0.95);\n  border: 1px solid rgba(0, 0, 0, 0.08);\n  color: #1a202c;\n}\n\n.notification .close-button {\n  background: transparent;\n  border: none;\n  color: #6b7280;\n  cursor: pointer;\n  padding: 4px;\n  margin-left: 12px;\n  border-radius: 4px;\n  transition: all 0.15s ease;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 20px;\n  height: 20px;\n  flex-shrink: 0;\n}\n\n.notification .close-button:hover {\n  background: rgba(107, 114, 128, 0.1);\n  color: #374151;\n}\n\n.dark .notification .close-button {\n  color: #9ca3af;\n}\n\n.dark .notification .close-button:hover {\n  background: rgba(156, 163, 175, 0.1);\n  color: #e5e7eb;\n}\n\n.notification-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: 2px;\n}\n\n.notification-message {\n  font-weight: 500;\n  font-size: 13px;\n  line-height: 1.4;\n  margin: 0;\n}\n\n.notification-time {\n  font-size: 11px;\n  opacity: 0.6;\n  font-weight: 400;\n}\n\n@keyframes slideInRight {\n  from {\n    transform: translateX(100%) scale(0.9);\n    opacity: 0;\n  }\n  to {\n    transform: translateX(0) scale(1);\n    opacity: 1;\n  }\n}\n\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n  50% {\n    opacity: 0.7;\n    transform: scale(1.1);\n  }\n}\n\n/* Professional Scrollbar Styling */\n.table-container::-webkit-scrollbar,\n.detail-table-wrapper::-webkit-scrollbar {\n  width: 6px;\n  height: 6px;\n}\n\n.table-container::-webkit-scrollbar-track,\n.detail-table-wrapper::-webkit-scrollbar-track {\n  background: transparent;\n}\n\n.table-container::-webkit-scrollbar-thumb,\n.detail-table-wrapper::-webkit-scrollbar-thumb {\n  background: rgba(107, 114, 128, 0.3);\n  border-radius: 3px;\n}\n\n.table-container::-webkit-scrollbar-thumb:hover,\n.detail-table-wrapper::-webkit-scrollbar-thumb:hover {\n  background: rgba(107, 114, 128, 0.5);\n}\n\n.dark .table-container::-webkit-scrollbar-thumb,\n.dark .detail-table-wrapper::-webkit-scrollbar-thumb {\n  background: rgba(156, 163, 175, 0.3);\n}\n\n.dark .table-container::-webkit-scrollbar-thumb:hover,\n.dark .detail-table-wrapper::-webkit-scrollbar-thumb:hover {\n  background: rgba(156, 163, 175, 0.5);\n}\n\n/* Additional Animations */\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .container {\n    padding: 16px;\n  }\n\n  .header {\n    padding: 20px 24px;\n    margin-bottom: 24px;\n  }\n\n  .card {\n    padding: 24px;\n  }\n}\n\n@media (max-width: 768px) {\n  .container {\n    padding: 12px;\n  }\n\n  .header {\n    flex-direction: row;\n    gap: 20px;\n    padding: 20px;\n    text-align: center;\n  }\n\n  .header-actions {\n    justify-content: center;\n  }\n\n  .logo h1 {\n    font-size: 24px;\n  }\n\n  .card {\n    padding: 20px;\n    border-radius: 16px;\n  }\n\n  .events-table th,\n  .events-table td {\n    padding: 12px 16px;\n    font-size: 13px;\n  }\n\n  .detail-header {\n    flex-direction: row;\n    align-items: center;\n    gap: 16px;\n  }\n\n  .detail-header h2 {\n    font-size: 24px;\n  }\n\n  .notifications-container {\n    left: 12px;\n    right: 12px;\n    top: 12px;\n    max-width: none;\n  }\n\n  .notification {\n    padding: 16px 20px;\n  }\n}\n\n@media (max-width: 480px) {\n  .key-cell {\n    width: 35%;\n    padding: 16px;\n    font-size: 12px;\n  }\n\n  .value-cell {\n    width: 65%;\n    padding: 16px;\n    font-size: 14px;\n  }\n\n  .back-button {\n    padding: 12px 20px;\n    font-size: 14px;\n  }\n\n  .theme-toggle, .test-notification-button, .logout-button {\n    padding: 10px;\n  }\n}\n\n.table-container {\n  max-height: 500px;\n  overflow-y: auto;\n  overflow-x: auto;\n  border-radius: 8px;\n  background: transparent;\n  border: 1px solid rgba(255, 255, 255, 0.08);\n}\n\n.dark .table-container {\n  border: 1px solid rgba(255, 255, 255, 0.06);\n}\n\n.light .table-container {\n  border: 1px solid rgba(0, 0, 0, 0.06);\n}\n\n.events-table {\n  width: 100%;\n  border-collapse: separate;\n  border-spacing: 0;\n  font-size: 13px;\n}\n\n.events-table th {\n  position: sticky;\n  top: 0;\n  background: #f8fafc;\n  color: #374151;\n  z-index: 10;\n  padding: 12px 16px;\n  font-weight: 600;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n  font-size: 11px;\n  border: none;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.06);\n}\n\n.dark .events-table th {\n  background: #1a202c;\n  color: #e2e8f0;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.06);\n}\n\n.events-table td {\n  padding: 12px 16px;\n  border: none;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.04);\n  white-space: pre-wrap;\n  word-break: break-word;\n  transition: background-color 0.15s ease;\n  font-size: 13px;\n  line-height: 1.5;\n}\n\n.events-table tbody tr:hover {\n  background: rgba(59, 130, 246, 0.04);\n}\n\n.dark .events-table td {\n  border-bottom: 1px solid rgba(255, 255, 255, 0.04);\n  color: #e2e8f0;\n}\n\n.light .events-table td {\n  border-bottom: 1px solid rgba(0, 0, 0, 0.04);\n  color: #374151;\n}\n\n.dark .events-table tbody tr:hover {\n  background: rgba(59, 130, 246, 0.08);\n}\n\n.light .events-table tbody tr:hover {\n  background: rgba(59, 130, 246, 0.04);\n}\n\n.eye-button {\n  background: #2563eb;\n  border: none;\n  cursor: pointer;\n  padding: 6px;\n  border-radius: 6px;\n  transition: all 0.15s ease;\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n  width: 28px;\n  height: 28px;\n}\n\n.eye-button:hover {\n  background: #1d4ed8;\n  transform: translateY(-1px);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);\n}\n\n.eye-button:active {\n  transform: translateY(0);\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n}\n\n/* Detail View Styles */\n.detail-view {\n  width: 100%;\n  height: 100%;\n  animation: fadeInUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.detail-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 24px;\n  gap: 16px;\n  padding: 0 4px;\n}\n\n.detail-header h2 {\n  margin: 0;\n  font-size: 20px;\n  font-weight: 600;\n  color: #1a202c;\n  letter-spacing: -0.025em;\n}\n\n.dark .detail-header h2 {\n  color: #ffffff;\n}\n\n.back-button {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 16px;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  font-size: 14px;\n  font-weight: 500;\n  border: 1px solid rgba(37, 99, 235, 0.2);\n  background: #2563eb;\n  color: white;\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n}\n\n.back-button:hover {\n  background: #1d4ed8;\n  transform: translateY(-1px);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);\n}\n\n.back-button:active {\n  transform: translateY(0);\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n}\n\n/* Light Mode Styles */\n.light .back-button {\n  background: #e0e7ff;\n  color: #4f46e5;\n}\n\n.light .back-button:hover {\n  background: #c7d2fe;\n  transform: translateX(-3px);\n}\n\n.light .detail-header h2 {\n  color: #1f2937;\n}\n\n.light .detail-table {\n  background: #ffffff;\n  border: 1px solid #e5e7eb;\n}\n\n.light .key-cell {\n  background-color: #f3f4f6;\n  color: #4b5563;\n}\n\n.light .value-cell {\n  color: #1f2937;\n}\n\n.light .array-item {\n  background: #f9fafb;\n  border: 1px solid #e5e7eb;\n}\n\n.light .sub-key {\n  color: #4b5563;\n}\n\n.light .sub-value {\n  color: #1f2937;\n}\n\n/* Dark Mode Styles */\n.dark .back-button {\n  background: #312e81;\n  color: #e0e7ff;\n}\n\n.dark .back-button:hover {\n  background: #3730a3;\n  transform: translateX(-3px);\n}\n\n.dark .detail-header h2 {\n  color: #f3f4f6;\n}\n\n.dark .detail-table {\n  background: #1f2937;\n  border: 1px solid #374151;\n}\n\n.dark .key-cell {\n  background-color: #111827;\n  color: #9ca3af;\n  border-bottom: 1px solid #374151;\n}\n\n.dark .value-cell {\n  color: #e5e7eb;\n  border-bottom: 1px solid #374151;\n}\n\n.dark .array-item {\n  background: #111827;\n  border: 1px solid #374151;\n}\n\n.dark .sub-key {\n  color: #9ca3af;\n}\n\n.dark .sub-value {\n  color: #e5e7eb;\n}\n\n/* Common Styles */\n.detail-table-wrapper {\n  padding: 0 4px;\n  border-radius: 8px;\n  overflow: hidden;\n  background: transparent;\n  border: 1px solid rgba(255, 255, 255, 0.08);\n}\n\n.dark .detail-table-wrapper {\n  border: 1px solid rgba(255, 255, 255, 0.06);\n}\n\n.light .detail-table-wrapper {\n  border: 1px solid rgba(0, 0, 0, 0.06);\n}\n\n.detail-table {\n  width: 100%;\n  border-collapse: separate;\n  border-spacing: 0;\n  border-radius: 8px;\n  overflow: hidden;\n}\n\n.key-cell {\n  width: 25%;\n  padding: 12px 16px;\n  font-weight: 600;\n  font-size: 11px;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n  background: #f8fafc;\n  color: #374151;\n  border: none;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.06);\n}\n\n.dark .key-cell {\n  background: #1a202c;\n  color: #e2e8f0;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.06);\n}\n\n.value-cell {\n  width: 75%;\n  padding: 12px 16px;\n  font-size: 13px;\n  line-height: 1.5;\n  border: none;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.04);\n  background: rgba(255, 255, 255, 0.5);\n  color: #374151;\n}\n\n.dark .value-cell {\n  background: rgba(26, 32, 44, 0.3);\n  color: #e2e8f0;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.04);\n}\n\n.light .value-cell {\n  background: rgba(255, 255, 255, 0.5);\n  color: #374151;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.04);\n}\n\n.array-value,\n.object-value {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n}\n\n.array-item {\n  padding: 16px 20px;\n  border-radius: 12px;\n  margin: 8px 0;\n  background: rgba(59, 130, 246, 0.1);\n  border: 1px solid rgba(59, 130, 246, 0.2);\n  transition: all 0.2s ease;\n}\n\n.array-item:hover {\n  background: rgba(59, 130, 246, 0.15);\n  transform: translateX(4px);\n}\n\n.sub-item {\n  display: flex;\n  gap: 16px;\n  padding: 8px 0;\n  align-items: flex-start;\n}\n\n.sub-key {\n  font-weight: 600;\n  min-width: 140px;\n  color: #3b82f6;\n  font-size: 13px;\n  text-transform: uppercase;\n  letter-spacing: 0.5px;\n}\n\n.sub-value {\n  flex: 1;\n  word-break: break-word;\n}\n\n/* Hover Effects */\n.detail-table tr:hover .key-cell {\n  background-color: #374151;\n  color: #e5e7eb;\n}\n\n.dark .detail-table tr:hover {\n  background-color: #374151;\n}\n\n.light .detail-table tr:hover {\n  background-color: #f9fafb;\n}\n\n/* Transitions */\n.back-button,\n.detail-table tr {\n  transition: all 0.2s ease-in-out;\n}\n\n.empty-notifications,\n.empty-table {\n  text-align: center;\n  padding: 40px 32px;\n  color: #6b7280;\n  font-size: 14px;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: 8px;\n}\n\n.empty-table {\n  background: rgba(107, 114, 128, 0.05);\n  border-radius: 8px;\n  margin: 16px 0;\n  border: 1px dashed rgba(107, 114, 128, 0.2);\n}\n\n.empty-table::before {\n  content: '📊';\n  font-size: 32px;\n  display: block;\n  margin-bottom: 8px;\n  opacity: 0.4;\n}\n\n.empty-notifications::before {\n  content: '🔔';\n  font-size: 32px;\n  display: block;\n  margin-bottom: 8px;\n  opacity: 0.4;\n}\n\n.dark .empty-notifications,\n.dark .empty-table {\n  color: #9ca3af;\n  background: rgba(156, 163, 175, 0.05);\n  border-color: rgba(156, 163, 175, 0.2);\n}\n\n.light .empty-notifications,\n.light .empty-table {\n  color: #6b7280;\n}\n\n.header-actions {\n  display: flex;\n  gap: 10px;\n  align-items: center;\n}\n\n.logout-button {\n  background: none;\n  border: none;\n  cursor: pointer;\n  color: inherit;\n  padding: 5px;\n  border-radius: 5px;\n  transition: background-color 0.2s;\n}\n\n.logout-button:hover {\n  background-color: rgba(0, 0, 0, 0.1);\n}\n\n.dark .logout-button:hover {\n  background-color: rgba(255, 255, 255, 0.1);\n}\n\n/* Notification Permission Modal */\n.notification-modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.7);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 10000;\n  animation: fadeIn 0.3s ease-out;\n}\n\n.notification-modal {\n  background: white;\n  border-radius: 16px;\n  padding: 0;\n  max-width: 480px;\n  width: 90%;\n  max-height: 80vh;\n  overflow: hidden;\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n  animation: slideInUp 0.3s ease-out;\n  position: relative;\n}\n\n.dark .notification-modal {\n  background: #2d3748;\n  color: white;\n}\n\n.notification-modal-header {\n  position: relative;\n  padding: 16px 20px 0 20px;\n}\n\n.modal-close-button {\n  position: absolute;\n  top: 16px;\n  right: 16px;\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: 8px;\n  border-radius: 50%;\n  color: #718096;\n  transition: all 0.2s ease;\n}\n\n.modal-close-button:hover {\n  background: rgba(0, 0, 0, 0.1);\n  color: #2d3748;\n}\n\n.dark .modal-close-button {\n  color: #a0aec0;\n}\n\n.dark .modal-close-button:hover {\n  background: rgba(255, 255, 255, 0.1);\n  color: white;\n}\n\n.notification-modal-content {\n  padding: 20px 32px;\n  text-align: center;\n}\n\n.modal-icon {\n  margin-bottom: 16px;\n  color: #3182ce;\n}\n\n.modal-icon.denied {\n  color: #e53e3e;\n}\n\n.modal-icon.default {\n  color: #38a169;\n  animation: pulse 2s infinite;\n}\n\n.notification-modal-content h2 {\n  margin: 0 0 12px 0;\n  font-size: 24px;\n  font-weight: 600;\n  color: #2d3748;\n}\n\n.dark .notification-modal-content h2 {\n  color: white;\n}\n\n.notification-modal-content p {\n  margin: 0 0 20px 0;\n  color: #4a5568;\n  line-height: 1.6;\n  font-size: 16px;\n}\n\n.dark .notification-modal-content p {\n  color: #cbd5e0;\n}\n\n.notification-instructions {\n  background: #f7fafc;\n  border-radius: 8px;\n  padding: 16px;\n  margin: 20px 0;\n  text-align: left;\n}\n\n.dark .notification-instructions {\n  background: #4a5568;\n}\n\n.instruction-item {\n  margin: 8px 0;\n  color: #2d3748;\n  font-size: 14px;\n  line-height: 1.5;\n}\n\n.dark .instruction-item {\n  color: #e2e8f0;\n}\n\n.notification-modal-actions {\n  padding: 20px 32px 32px 32px;\n  display: flex;\n  gap: 12px;\n  justify-content: center;\n  flex-wrap: wrap;\n}\n\n.modal-primary-button, .modal-secondary-button {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 12px 24px;\n  border: none;\n  border-radius: 8px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  font-size: 14px;\n  min-width: 120px;\n  justify-content: center;\n}\n\n.modal-primary-button {\n  background: #3182ce;\n  color: white;\n}\n\n.modal-primary-button:hover {\n  background: #2c5aa0;\n  transform: translateY(-1px);\n}\n\n.modal-secondary-button {\n  background: #e2e8f0;\n  color: #4a5568;\n}\n\n.modal-secondary-button:hover {\n  background: #cbd5e0;\n}\n\n.dark .modal-secondary-button {\n  background: #4a5568;\n  color: #e2e8f0;\n}\n\n.dark .modal-secondary-button:hover {\n  background: #718096;\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes slideInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px) scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0) scale(1);\n  }\n}\n\n@keyframes pulse {\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.1);\n  }\n}\n", ".react-tabs {\n  -webkit-tap-highlight-color: transparent;\n}\n\n.react-tabs__tab-list {\n  border-bottom: 1px solid #aaa;\n  margin: 0 0 10px;\n  padding: 0;\n}\n\n.react-tabs__tab {\n  display: inline-block;\n  border: 1px solid transparent;\n  border-bottom: none;\n  bottom: -1px;\n  position: relative;\n  list-style: none;\n  padding: 6px 12px;\n  cursor: pointer;\n}\n\n.react-tabs__tab--selected {\n  background: #fff;\n  border-color: #aaa;\n  color: black;\n  border-radius: 5px 5px 0 0;\n}\n\n.react-tabs__tab--disabled {\n  color: GrayText;\n  cursor: default;\n}\n\n.react-tabs__tab:focus {\n  outline: none;\n}\n\n.react-tabs__tab:focus:after {\n  content: '';\n  position: absolute;\n  height: 5px;\n  left: -4px;\n  right: -4px;\n  bottom: -5px;\n  background: #fff;\n}\n\n.react-tabs__tab-panel {\n  display: none;\n}\n\n.react-tabs__tab-panel--selected {\n  display: block;\n}\n", ".App {\n  text-align: center;\n}\n\n.App-logo {\n  height: 40vmin;\n  pointer-events: none;\n}\n\n@media (prefers-reduced-motion: no-preference) {\n  .App-logo {\n    animation: App-logo-spin infinite 20s linear;\n  }\n}\n\n.App-header {\n  background-color: #282c34;\n  min-height: 100vh;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  font-size: calc(10px + 2vmin);\n  color: white;\n}\n\n.App-link {\n  color: #61dafb;\n}\n\n@keyframes App-logo-spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n"], "names": [], "sourceRoot": ""}