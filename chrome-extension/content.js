(function () {
  if (document.querySelector(".ue-sidebar-container")) return;
  const sidebarContainer = document.createElement("div");
  sidebarContainer.className = "ue-sidebar-container show";
  sidebarContainer.style.position = "fixed";
  sidebarContainer.style.top = "0";
  sidebarContainer.style.right = "0";
  sidebarContainer.style.width = "400px";
  sidebarContainer.style.height = "100%";
  sidebarContainer.style.zIndex = "9999";
  sidebarContainer.style.background = "white";
  sidebarContainer.style.boxShadow = "-2px 0 5px rgba(0,0,0,0.2)";
  sidebarContainer.style.display = "flex";
  sidebarContainer.style.flexDirection = "column";

  // Create title
  const title = document.createElement("div");
  title.innerText = "Quickbuzz";
  title.style.fontSize = "18px";
  title.style.fontWeight = "bold";
  title.style.textAlign = "center";
  title.style.padding = "15px";
  title.style.background = "#1a202c";
  title.style.color = "white";

  // Create close button
  const closeButton = document.createElement("button");
  closeButton.innerText = "✖";
  closeButton.style.position = "absolute";
  closeButton.style.top = "10px";
  closeButton.style.right = "10px";
  closeButton.style.background = "white";
  closeButton.style.color = "#1a202c";
  closeButton.style.border = "none";
  closeButton.style.borderRadius = "5px";
  closeButton.style.padding = "5px 10px";
  closeButton.style.cursor = "pointer";
  closeButton.style.fontSize = "16px";

  let isSidebarOpen = false; // Track sidebar state

  closeButton.addEventListener("click", function () {
    toggleSidebar();
  });

  // Create iframe
  const iframe = document.createElement("iframe");
  iframe.src = chrome.runtime.getURL("build/index.html");
  iframe.style.width = "100%";
  iframe.style.height = "100%";
  iframe.style.border = "none";
  iframe.style.flex = "1";

  sidebarContainer.appendChild(title);
  sidebarContainer.appendChild(closeButton);
  sidebarContainer.appendChild(iframe);

  // Create toggle button
  const toggleButton = document.createElement("button");
  toggleButton.innerHTML =
    '<img src="https://ik.imagekit.io/nz0mhbk9t/shopify-icon.png?updatedAt=1743679874682" alt="Toggle Sidebar" style="width: 100%; height: 100%; border-radius: 50%;">';
  toggleButton.style.position = "fixed";
  toggleButton.style.top = "50%";
  toggleButton.style.right = "10px"; // Initially positioned at right end
  toggleButton.style.transform = "translateY(-50%)";
  toggleButton.style.background = "#1a202c";
  toggleButton.style.color = "white";
  toggleButton.style.border = "none";
  toggleButton.style.borderRadius = "50%";
  toggleButton.style.width = "30px";
  toggleButton.style.height = "30px";
  toggleButton.style.padding = "0";
  toggleButton.style.cursor = "pointer";
  toggleButton.style.zIndex = "10000";
  toggleButton.style.transition = "right 0.3s ease, left 0.3s ease"; // Add transition for smooth movement

  toggleButton.addEventListener("click", function () {
    toggleSidebar();
  });

  // Function to toggle sidebar and update toggle button position
  function toggleSidebar() {
    if (isSidebarOpen) {
      // Close the sidebar
      sidebarContainer.style.transform = "translateX(100%)"; // Slide out
      document.body.style.width = "100%"; // Restore body width

      // Move toggle button to right side
      toggleButton.style.right = "10px"; // Position at the right end
      toggleButton.style.left = "auto";
    } else {
      // Open the sidebar
      sidebarContainer.style.transform = "translateX(0)"; // Slide in
      document.body.style.width = "calc(100% - 400px)"; // Adjust body width

      // Move toggle button to the end of the screen
      toggleButton.style.right = "0"; // Align to the right edge of the screen
      toggleButton.style.left = "auto";
    }
    isSidebarOpen = !isSidebarOpen; // Toggle state
  }

  // Handle window resize to adjust toggle button position when sidebar is open
  window.addEventListener("resize", function () {
    if (isSidebarOpen) {
      toggleButton.style.left = window.innerWidth - 415 + "px";
    }
  });

  // Append the toggle button to the body
  document.body.appendChild(toggleButton);

  // Append the sidebar container to the body
  document.body.appendChild(sidebarContainer);
  sidebarContainer.style.transition = "transform 0.3s ease"; // Add transition for sliding effect
  sidebarContainer.style.transform = "translateX(100%)"; // Start hidden

  // Send a message to show a notification
  chrome.runtime.sendMessage({ action: "show_notification" });

  // Listen for messages from the React app (iframe)
  window.addEventListener('message', function(event) {
    // Make sure the message is from our iframe
    if (event.source === iframe.contentWindow) {
      console.log('Content script received message from React app:', event.data);

      // Forward the message to background script
      if (event.data.action === 'create_chrome_notification') {
        chrome.runtime.sendMessage(event.data);
      }
    }
  });
})();
