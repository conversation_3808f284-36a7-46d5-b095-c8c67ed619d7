/* eslint-disable no-undef */
let isOpen = false;
let activeTabId = null;

chrome.action.onClicked.addListener((tab) => {
    activeTabId = tab.id;
    isOpen = !isOpen;
    if (isOpen) {
        chrome.scripting.executeScript({
            target: { tabId: tab.id },
            files: ['content.js']
        });
    } else {
        chrome.scripting.executeScript({
            target: { tabId: tab.id },
            function: () => {
                const sidebarContainer = document.querySelector('.ue-sidebar-container');
                if (sidebarContainer) {
                    sidebarContainer.remove();
                }
            }
        });
    }
});

// Handle messages from content script and React app
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('Background received message:', message);
    if (message.action === 'create_chrome_notification') {
        // Handle Chrome notification creation
        createChromeNotification(message.data);
        sendResponse({ success: true });
    } else if (message.action === 'test_notification') {
        // Handle test notification
        createTestNotification();
        sendResponse({ success: true });
    } else if (message.action === 'open_extension') {
        // Open extension when notification is clicked
        openExtension();
        sendResponse({ success: true });
    }

    return true; // Keep message channel open
});

// Create Chrome native notification
function createChromeNotification(notificationData) {
    const { title, message, iconUrl, eventId } = notificationData;

    console.log('Creating Chrome notification:', notificationData);

    const notificationOptions = {
        type: 'basic',
        iconUrl: chrome.runtime.getURL('icons/icon48.png'), // Use extension icon
        title: title || 'Quickbuzz Notification',
        message: message || 'New notification received',
        priority: 2,
        requireInteraction: false,
        silent: false
    };

    const notificationId = eventId || `notification_${Date.now()}`;

    chrome.notifications.create(notificationId, notificationOptions, (createdId) => {
        if (chrome.runtime.lastError) {
            console.error('Notification creation failed:', chrome.runtime.lastError);
        } else {
            console.log('Notification created successfully:', createdId);

            // Auto-clear after 8 seconds
            setTimeout(() => {
                chrome.notifications.clear(createdId);
            }, 8000);
        }
    });
}

// Create test notification
function createTestNotification() {
    createChromeNotification({
        title: '🔔 Test Notification',
        message: 'This is a test notification from Quickbuzz!',
        eventId: `test_${Date.now()}`
    });
}

// Handle notification clicks
chrome.notifications.onClicked.addListener((notificationId) => {
    console.log('Notification clicked:', notificationId);
    chrome.notifications.clear(notificationId);
    openExtension();
});

// Function to open extension
function openExtension() {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs[0]) {
            activeTabId = tabs[0].id;
            chrome.scripting.executeScript({
                target: { tabId: activeTabId },
                files: ['content.js']
            });
            isOpen = true;
        }
    });
}
