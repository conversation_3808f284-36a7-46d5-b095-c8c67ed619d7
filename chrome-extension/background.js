/* eslint-disable no-undef */
let isOpen = false;
let activeTabId = null;

// Handle extension icon click
chrome.action.onClicked.addListener((tab) => {
    activeTabId = tab.id;
    isOpen = !isOpen;
    if (isOpen) {
        chrome.scripting.executeScript({
            target: { tabId: tab.id },
            files: ['content.js']
        });
    } else {
        chrome.scripting.executeScript({
            target: { tabId: tab.id },
            function: () => {
                const sidebarContainer = document.querySelector('.ue-sidebar-container');
                if (sidebarContainer) {
                    sidebarContainer.remove();
                }
            }
        });
    }
});

// Handle messages from content script and React app
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('Background received message:', message);

    if (message.action === 'show_notification') {
        // Handle basic notification request
        sendResponse({ success: true });
    } else if (message.action === 'create_chrome_notification') {
        // Handle Kafka notification from React app
        createChromeNotification(message.data);
        sendResponse({ success: true });
    } else if (message.action === 'open_extension') {
        // Open extension when notification is clicked
        openExtension();
        sendResponse({ success: true });
    }

    return true; // Keep message channel open for async response
});

// Create Chrome native notification
function createChromeNotification(notificationData) {
    const { title, message, iconUrl, eventId } = notificationData;

    const notificationOptions = {
        type: 'basic',
        iconUrl: iconUrl || chrome.runtime.getURL('icon.png'),
        title: title || 'Quickbuzz Notification',
        message: message || 'New notification received',
        priority: 2,
        requireInteraction: true
    };

    chrome.notifications.create(eventId || `notification_${Date.now()}`, notificationOptions, (notificationId) => {
        if (chrome.runtime.lastError) {
            console.error('Notification creation failed:', chrome.runtime.lastError);
        } else {
            console.log('Notification created:', notificationId);
        }
    });
}

// Handle notification clicks
chrome.notifications.onClicked.addListener((notificationId) => {
    console.log('Notification clicked:', notificationId);

    // Clear the notification
    chrome.notifications.clear(notificationId);

    // Open the extension
    openExtension();
});

// Function to open extension
function openExtension() {
    chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
        if (tabs[0]) {
            activeTabId = tabs[0].id;

            // Check if extension is already open
            chrome.scripting.executeScript({
                target: { tabId: activeTabId },
                function: () => {
                    return !!document.querySelector('.ue-sidebar-container');
                }
            }, (results) => {
                if (results && results[0] && !results[0].result) {
                    // Extension not open, open it
                    chrome.scripting.executeScript({
                        target: { tabId: activeTabId },
                        files: ['content.js']
                    });
                    isOpen = true;
                } else {
                    // Extension already open, just focus it
                    chrome.scripting.executeScript({
                        target: { tabId: activeTabId },
                        function: () => {
                            const sidebar = document.querySelector('.ue-sidebar-container');
                            if (sidebar) {
                                sidebar.style.transform = 'translateX(0)';
                            }
                        }
                    });
                }
            });
        }
    });
}
