/* eslint-disable no-undef */
let isOpen = false;

chrome.action.onClicked.addListener((tab) => {
    isOpen = !isOpen;
    if (isOpen) {
        chrome.scripting.executeScript({
            target: { tabId: tab.id },
            files: ['content.js']
        });
    } else {
        chrome.scripting.executeScript({
            target: { tabId: tab.id },
            function: () => {
                const sidebarContainer = document.querySelector('.ue-sidebar-container');
                if (sidebarContainer) {
                    sidebarContainer.remove();
                }
            }
        });
    }
});
