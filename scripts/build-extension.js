const fs = require('fs');
const path = require('path');

// Paths
const buildDir = path.join(__dirname, '../build');
const extensionBuildDir = path.join(__dirname, '../chrome-extension/build');

console.log('🚀 Building Chrome Extension...');

// Function to copy directory recursively
function copyDir(src, dest) {
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }
  
  const entries = fs.readdirSync(src, { withFileTypes: true });
  
  for (let entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);
    
    if (entry.isDirectory()) {
      copyDir(srcPath, destPath);
    } else {
      fs.copyFileSync(srcPath, destPath);
    }
  }
}

// Function to fix paths in HTML file
function fixHtmlPaths(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  
  // Fix script and link paths to be relative
  content = content.replace(/src="\/static\//g, 'src="./static/');
  content = content.replace(/href="\/static\//g, 'href="./static/');
  
  // Fix favicon and other asset paths
  content = content.replace(/href="\/favicon\.ico"/g, 'href="./favicon.ico"');
  content = content.replace(/href="\/logo192\.png"/g, 'href="./logo192.png"');
  content = content.replace(/href="\/logo512\.png"/g, 'href="./logo512.png"');
  content = content.replace(/href="\/manifest\.json"/g, 'href="./manifest.json"');
  
  fs.writeFileSync(filePath, content);
}

// Function to fix paths in asset manifest
function fixAssetManifest(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let manifest = JSON.parse(content);
  
  // Fix all paths to be relative
  for (let key in manifest.files) {
    if (manifest.files[key].startsWith('/')) {
      manifest.files[key] = '.' + manifest.files[key];
    }
  }
  
  // Fix entrypoints
  if (manifest.entrypoints) {
    manifest.entrypoints = manifest.entrypoints.map(entry =>
      entry.startsWith('/') ? '.' + entry : (entry.startsWith('static/') ? './' + entry : entry)
    );
  }
  
  fs.writeFileSync(filePath, JSON.stringify(manifest, null, 2));
}

try {
  // Step 1: Clean the extension build directory
  if (fs.existsSync(extensionBuildDir)) {
    fs.rmSync(extensionBuildDir, { recursive: true, force: true });
  }
  
  console.log('✅ Cleaned extension build directory');
  
  // Step 2: Copy build files to extension directory
  copyDir(buildDir, extensionBuildDir);
  console.log('✅ Copied build files to extension directory');
  
  // Step 3: Fix paths in index.html
  const indexPath = path.join(extensionBuildDir, 'index.html');
  if (fs.existsSync(indexPath)) {
    fixHtmlPaths(indexPath);
    console.log('✅ Fixed paths in index.html');
  }
  
  // Step 4: Fix paths in asset-manifest.json
  const assetManifestPath = path.join(extensionBuildDir, 'asset-manifest.json');
  if (fs.existsSync(assetManifestPath)) {
    fixAssetManifest(assetManifestPath);
    console.log('✅ Fixed paths in asset-manifest.json');
  }
  
  console.log('🎉 Chrome Extension build completed successfully!');
  console.log('📁 Extension files are ready in: chrome-extension/');
  console.log('');
  console.log('Next steps:');
  console.log('1. Open Chrome and go to chrome://extensions/');
  console.log('2. Enable "Developer mode"');
  console.log('3. Click "Load unpacked" and select the chrome-extension folder');
  
} catch (error) {
  console.error('❌ Error building extension:', error);
  process.exit(1);
}
