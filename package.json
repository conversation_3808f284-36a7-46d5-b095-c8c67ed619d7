{"name": "shopify-react-extension", "version": "0.1.0", "private": true, "homepage": "./", "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.8.4", "chart.js": "^4.4.4", "kafkajs": "^2.2.4", "lucide-react": "^0.451.0", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-dom": "^18.3.1", "react-router-dom": "^7.4.0", "react-scripts": "5.0.1", "react-tabs": "^6.0.2", "react-toastify": "^11.0.5", "recharts": "^2.12.7", "web-vitals": "^2.1.4"}, "scripts": {"start": "PORT=4000 react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}