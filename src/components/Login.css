.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #f7fafc;
  }
  
  .login-box {
    background-color: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
  }
  
  .login-box h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: #2d3748;
  }
  
  .login-content {
    max-width: 500px;
    margin: 4rem auto;
    padding: 0 1rem;
    box-sizing: border-box;
  }
  
  .card {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    width: 100%;
    box-sizing: border-box;
  }
  
  .card h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--text-color);
  }
  
  .form-group {
    margin-bottom: 1.5rem;
    width: 100%;
    box-sizing: border-box;
  }
  
  .form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
  }
  
  .form-group input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background: var(--card-bg);
    color: var(--text-color);
    font-size: 1rem;
    outline: none;
    box-sizing: border-box;
    transition: border-color 0.2s;
  }
  
  .verify-button {
    width: 100%;
    padding: 1rem;
    margin-top: 1rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .verify-button:hover {
    background: var(--primary-color-dark);
  }
  
  .error-alert {
    background-color: var(--error-bg);
    color: var(--error-color);
    padding: 0.75rem;
    border-radius: 8px;
    margin-bottom: 1rem;
  }
  
  /* Dark mode variables */
  .dark {
    --primary-color: #8884d8;
    --primary-color-dark: #7673be;
    --card-bg: #2a2a2a;
    --border-color: #404040;
    --text-color: #ffffff;
    --error-bg: #ff000020;
    --error-color: #ff6b6b;
  }
  
  /* Light mode variables */
  .light {
    --primary-color: #8884d8;
    --primary-color-dark: #7673be;
    --card-bg: #ffffff;
    --border-color: #e0e0e0;
    --text-color: #333333;
    --error-bg: #ff000010;
    --error-color: #dc3545;
  }

  form {
    width: 100%;
    box-sizing: border-box;
  }