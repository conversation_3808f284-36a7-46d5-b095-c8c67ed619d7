import React, { useState, useEffect } from 'react';
import { Sun, Moon, X } from 'lucide-react';
import { LineChart, Line, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
// import { Kafka } from "kafkajs";
import './Dashboard.css';

const Dashboard = () => {
  const [activeTab, setActiveTab] = useState('Dashboard');
  const [isDarkMode, setIsDarkMode] = useState(true);
  const [notifications, setNotifications] = useState([]);

  const toggleTheme = () => setIsDarkMode(!isDarkMode);

  const TabButton = ({ tab }) => (
    <button
      onClick={() => setActiveTab(tab)}
      className={`tab-button ${activeTab === tab ? 'active' : ''}`}
    >
      {tab}
    </button>
  );

  const Card = ({ title, value, subtitle }) => (
    <div className="card">
      <h3>{title}</h3>
      <p className="card-value">{value}</p>
      <p className="card-subtitle">{subtitle}</p>
    </div>
  );

  const Alert = ({ message, action }) => (
    <div className="alert">
      <span>{message}</span>
      <button className="action-button">{action}</button>
    </div>
  );

  const QuickAction = ({ text }) => (
    <div className="quick-action">
      <span>{text}</span>
      <button className="action-button">→</button>
    </div>
  );

  const Notification = ({ message, onClose }) => (
    <div className="notification">
      <span>{message}</span>
      <button onClick={onClose} className="close-button">
        <X size={16} />
      </button>
    </div>
  );



  // Notification system
  useEffect(() => {
    // Initialize SSE connection
    const eventSource = new EventSource('http://localhost:3000/events');

    // Handle incoming messages
    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log('Received event:', data);
        
        // Create notification from the event data
        const newNotification = {
          id: Date.now(),
          message: typeof data === 'object' ? JSON.stringify(data) : data,
          timestamp: new Date().toLocaleTimeString()
        };
        
        setNotifications(prevNotifications => [...prevNotifications, newNotification]);
      } catch (error) {
        console.error('Error processing event:', error);
      }
    };

    // Handle connection open
    eventSource.onopen = () => {
      console.log('SSE connection established');
    };

    // Handle errors and implement reconnection
    eventSource.onerror = (error) => {
      console.error('SSE connection error:', error);
      
      // Attempt to reconnect automatically (browser handles this by default)
      console.log('Attempting to reconnect...');
    };

    // Cleanup: close SSE connection when component unmounts
    return () => {
      console.log('Closing SSE connection');
      eventSource.close();
    };
  }, []); 

  // useEffect(() => {
  //   let orderId=25311;
  //   const interval = setInterval(() => {
  //     orderId++;
  //     const newNotification = {
  //       id: Date.now(),
  //       message: `Order #${orderId} placed at ${new Date().toLocaleTimeString()}`,
  //     };
  //     setNotifications(prevNotifications => [...prevNotifications, newNotification]);
  //   }, 5000);

  //   return () => clearInterval(interval);
  // }, []);

  const removeNotification = (id) => {
    setNotifications(prevNotifications => prevNotifications.filter(notification => notification.id !== id));
  };

  // Sample data for charts
  const revenueData = [
    { name: 'Jan', revenue: 4000 },
    { name: 'Feb', revenue: 3000 },
    { name: 'Mar', revenue: 5000 },
    { name: 'Apr', revenue: 4500 },
    { name: 'May', revenue: 6000 },
    { name: 'Jun', revenue: 5500 },
  ];

  const customerGrowthData = [
    { name: 'Jan', customers: 100 },
    { name: 'Feb', customers: 120 },
    { name: 'Mar', customers: 150 },
    { name: 'Apr', customers: 180 },
    { name: 'May', customers: 220 },
    { name: 'Jun', customers: 250 },
  ];

  const projectedRevenueData = [
    { year: '2024', revenue: 100000 },
    { year: '2025', revenue: 150000 },
    { year: '2026', revenue: 200000 },
    { year: '2027', revenue: 250000 },
  ];

  const inventoryData = [
    { name: 'The Collection Snowboard: Liquid', quantity: 50, price: 20, totalValue: 1000 },
    { name: 'The Multi-location Snowboard', quantity: 30, price: 30, totalValue: 900 },
    { name: 'The Complete Snowboard', quantity: 0, price: 25, totalValue: 0 },
    { name: 'The Collection Snowboard: Hydrogen', quantity: 75, price: 15, totalValue: 1125 },
  ];

  const customerData = [
    { name: 'John Doe', lastPurchase: '2024-03-15', totalPurchase: 1500 },
    { name: 'Jane Smith', lastPurchase: '2024-03-20', totalPurchase: 2200 },
    { name: 'Bob Johnson', lastPurchase: '2024-03-18', totalPurchase: 800 },
    { name: 'Alice Brown', lastPurchase: '2024-03-22', totalPurchase: 3000 },
  ];

  const salesGrowthData = [
    { year: '2020', sales: 50000 },
    { year: '2021', sales: 75000 },
    { year: '2022', sales: 100000 },
    { year: '2023', sales: 125000 },
    { year: '2024', sales: 150000 },
  ];

  const orderData = [
    { item: 'The Collection Snowboard: Liquid', quantity: 2, date: '2024-03-15', customer: 'John Doe' },
    { item: 'The Collection Snowboard: Liquid', quantity: 1, date: '2024-03-20', customer: 'Jane Smith' },
    { item: 'The Collection Snowboard: Liquid', quantity: 3, date: '2024-03-18', customer: 'Bob Johnson' },
    { item: 'The Collection Snowboard: Liquid', quantity: 4, date: '2024-03-22', customer: 'Alice Brown' },
  ];

  return (
    <div className={`app ${isDarkMode ? 'dark' : 'light'}`}>
      <div className="container">
        <div className="header">
          <div className="logo">
            <div className="logo-circle"></div>
            <h1>Shopify Notifier</h1>
          </div>
          <button onClick={toggleTheme} className="theme-toggle">
            {isDarkMode ? <Sun size={24} /> : <Moon size={24} />}
          </button>
        </div>

       {/*  <div className="tabs">
          <TabButton tab="Dashboard" />
          <TabButton tab="Insights" />
          <TabButton tab="Inventory" />
          <TabButton tab="Customers" />
          <TabButton tab="Sales" />
          <TabButton tab="Orders" />
        </div>
 */}
        <div className="notifications-container">
          {notifications.map(notification => (
            <Notification
              key={notification.id}
              message={notification.message}
              onClose={() => removeNotification(notification.id)}
            />
          ))}
        </div>

        {activeTab === 'Dashboard' && (
          <div className="dashboard-grid">
            <Card title="Financial Summary" value="$12,345.67" subtitle="Today's Revenue | ↑ 15% from yesterday" />
            <Card title="Abandoned Carts" value="7" subtitle="Potential Revenue: $892.50" />
            <Card title="Returns and Refunds" value="3" subtitle="Total Amount: $245.30" />
            
            <div className="card full-width">
              <h3>Custom Alerts</h3>
              <Alert message="Low Stock: Product X (5 left)" action="Restock" />
              <Alert message="High Traffic Alert: 500+ visitors" action="View" />
            </div>
            
            <div className="card full-width">
              <h3>Quick Actions</h3>
              <div className="quick-actions-grid">
                <QuickAction text="Recover Carts" />
                <QuickAction text="Process Returns" />
              </div>
            </div>
          </div>
        )}

        {activeTab === 'Insights' && (
          <div className="insights-grid">
            <div className="card full-width">
              <h2>Revenue Over Time</h2>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={revenueData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="revenue" stroke="#8884d8" activeDot={{ r: 8 }} />
                </LineChart>
              </ResponsiveContainer>
            </div>
            <div className="card full-width">
              <h2>Customer Growth</h2>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={customerGrowthData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="customers" stroke="#82ca9d" activeDot={{ r: 8 }} />
                </LineChart>
              </ResponsiveContainer>
            </div>
            <div className="card full-width">
              <h2>Projected Revenue</h2>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={projectedRevenueData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="year" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="revenue" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>
        )}

        {activeTab === 'Inventory' && (
          <div className="inventory-content">
            <div className="card full-width">
              <h2>Inventory</h2>
              <table className="inventory-table">
                <thead>
                  <tr>
                    <th>Product Name</th>
                    <th>Quantity</th>
                    <th>Unit Price</th>
                    <th>Total Value</th>
                  </tr>
                </thead>
                <tbody>
                  {inventoryData.map((item, index) => (
                    <tr key={index}>
                      <td>{item.name}</td>
                      <td>{item.quantity}</td>
                      <td>${item.price}</td>
                      <td>${item.totalValue}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <div className="card full-width">
              <h2>Inventory Distribution</h2>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={inventoryData}
                    dataKey="quantity"
                    nameKey="name"
                    cx="50%"
                    cy="50%"
                    outerRadius={100}
                    fill="#8884d8"
                    label
                  >
                    {inventoryData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={['#0088FE', '#00C49F', '#FFBB28', '#FF8042'][index % 4]} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
            <div className="card full-width">
              <h2>Low Stock Alerts</h2>
              {inventoryData.filter(item => item.quantity === 0).map((item, index) => (
                <Alert key={index} message={`${item.name} is out of stock!`} action="Restock" />
              ))}
            </div>
          </div>
        )}

        {activeTab === 'Customers' && (
          <div className="customers-content">
            <div className="card full-width">
              <h2>Customer Information</h2>
              <table className="customer-table">
                <thead>
                  <tr>
                    <th>Customer Name</th>
                    <th>Last Purchase Date</th>
                  </tr>
                </thead>
                <tbody>
                  {customerData.map((customer, index) => (
                    <tr key={index}>
                      <td>{customer.name}</td>
                      <td>{customer.lastPurchase}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
            <div className="card full-width">
              <h2>Customer Purchase Distribution</h2>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={customerData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="totalPurchase" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>
        )}

        {activeTab === 'Sales' && (
          <div className="sales-content">
            <div className="card full-width">
              <h2>Year-on-Year Sales Growth</h2>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={salesGrowthData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="year" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="sales" stroke="#8884d8" activeDot={{ r: 8 }} />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>
        )}

        {activeTab === 'Orders' && (
          <div className="orders-content">
            <div className="card full-width">
              <h2>Recent Orders</h2>
              <table className="order-table">
                <thead>
                  <tr>
                    <th>Ordered Item</th>
                    <th>Quantity</th>
                    <th>Date of Sale</th>
                    <th>Customer Name</th>
                  </tr>
                </thead>
                <tbody>
                  {orderData.map((order, index) => (
                    <tr key={index}>
                      <td>{order.item}</td>
                      <td>{order.quantity}</td>
                      <td>{order.date}</td>
                      <td>{order.customer}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Dashboard;