import React, { useState, useEffect } from "react";
import "./Login.css";
import { useNavigate } from "react-router-dom";
import { Sun, Moon, Bell, BellOff } from "lucide-react";
import { authService } from "../service/api";
import { BC_CREDENTIALS ,APP_NAME} from "../config/const";
import chromeNotificationService from "../service/chromeNotificationService";

const Login = () => {
  const navigate = useNavigate();
  const [email, setEmail] = useState("");
  const [secretKey, setSecretKey] = useState("");
  const [error, setError] = useState("");
  const [isDarkMode, setIsDarkMode] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [notificationPermission, setNotificationPermission] = useState('default');
  const [showNotificationPrompt, setShowNotificationPrompt] = useState(false);

  // Add useEffect to check localStorage on component mount
  useEffect(() => {
    const savedCredentials = localStorage.getItem(BC_CREDENTIALS);
    if (savedCredentials) {
      navigate("/dashboard");
    }

    // Check notification permission status
    if (chromeNotificationService.isNotificationSupported()) {
      const permission = chromeNotificationService.getPermissionStatus();
      setNotificationPermission(permission);

      // Show notification prompt if permission is default (not asked yet)
      if (permission === 'default') {
        setShowNotificationPrompt(true);
      }
    }
  }, [navigate]);

  // Basic email validation
  const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // Handle form submission
  const handleVerify = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    // Validate email
    if (!isValidEmail(email)) {
      setError("Please enter a valid email address");
      setIsLoading(false);
      return;
    }

    // Validate secret key (minimum 6 characters)
    if (secretKey.length < 6) {
      setError("Secret key must be at least 6 characters long");
      setIsLoading(false);
      return;
    }

    try {
      const response = await authService.login({
        email,
        secret_key: secretKey,
      });
      if (response.status === 200) {
        const credentials = {
          email: email,
          secret_key: secretKey,
          store: response?.data?.data?.store || "",
        };
        localStorage.setItem(BC_CREDENTIALS, JSON.stringify(credentials));
        // Navigate to dashboard
        navigate("/dashboard");
      }
    } catch (error) {
      setError(
        error.response?.data?.message ||
          "Authentication failed. Please try again."
      );
    } finally {
      setIsLoading(false);
    }
  };

  const toggleTheme = () => {
    setIsDarkMode(!isDarkMode);
  };

  // Handle notification permission request
  const handleNotificationPermission = async (action) => {
    if (action === 'allow') {
      const granted = await chromeNotificationService.requestPermission();
      setNotificationPermission(granted ? 'granted' : 'denied');

      if (granted) {
        // Test notification to confirm it works
        await chromeNotificationService.createTestNotification();
      }
    } else {
      // User dismissed the prompt
      setNotificationPermission('dismissed');
    }
    setShowNotificationPrompt(false);
  };

  return (
    <div className={`app ${isDarkMode ? "dark" : "light"}`}>
      <div className="container">
        <div className="header">
          <div className="logo">
            <h1>{APP_NAME}</h1>
          </div>
          <button onClick={toggleTheme} className="theme-toggle">
            {isDarkMode ? <Sun size={24} /> : <Moon size={24} />}
          </button>
        </div>

        <div className="login-content">
          {/* Notification Permission Prompt */}
          {showNotificationPrompt && (
            <div className="notification-prompt">
              <div className="notification-prompt-content">
                <div className="notification-prompt-header">
                  <Bell className="notification-icon" />
                  <h3>Enable Notifications</h3>
                </div>
                <p>
                  Get instant alerts for new orders and updates even when the browser is minimized.
                </p>
                <div className="notification-prompt-actions">
                  <button
                    onClick={() => handleNotificationPermission('allow')}
                    className="allow-button"
                  >
                    <Bell size={16} />
                    Allow Notifications
                  </button>
                  <button
                    onClick={() => handleNotificationPermission('dismiss')}
                    className="dismiss-button"
                  >
                    <BellOff size={16} />
                    Not Now
                  </button>
                </div>
              </div>
            </div>
          )}

          <div className="card">
            <h2>Welcome Back</h2>
            <form onSubmit={handleVerify}>
              <div className="form-group">
                <label htmlFor="email">Email Address</label>
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="secretKey">Secret Key</label>
                <input
                  type="password"
                  id="secretKey"
                  value={secretKey}
                  onChange={(e) => setSecretKey(e.target.value)}
                  placeholder="Enter your secret key"
                  required
                />
              </div>

              {error && (
                <div className="error-alert">
                  <span>{error}</span>
                </div>
              )}

              <button
                type="submit"
                className="verify-button"
                disabled={isLoading}
              >
                {isLoading ? "Verifying..." : "Verify Access"}
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
