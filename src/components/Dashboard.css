.app {
  min-height: 100vh;
  padding: 20px;
  transition: background-color 0.3s, color 0.3s;
}

.app.dark {
  background-color: #1a202c;
  color: white;
}

.app.light {
  background-color: #f7fafc;
  color: #1a202c;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.logo {
  display: flex;
  align-items: center;
}

.logo-circle {
  width: 40px;
  height: 40px;
  background-color: #ecc94b;
  border-radius: 50%;
  margin-right: 10px;
}

.theme-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
}

.dark .theme-toggle {
  background-color: #2d3748;
}

.light .theme-toggle {
  background-color: #e2e8f0;
}

.tabs {
  display: flex;
  background-color: #2d3748;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}

.tab-button {
  padding: 10px 20px;
  border: none;
  background: none;
  color: #a0aec0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.tab-button.active {
  background-color: #ecc94b;
  color: #1a202c;
}

.dashboard-grid,
.insights-grid,
.inventory-content,
.customers-content,
.sales-content,
.orders-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.card {
  background-color: #2d3748;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.light .card {
  background-color: white;
}

.card h3 {
  margin-top: 0;
  margin-bottom: 10px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #4299e1;
  margin: 10px 0;
}

.card-subtitle {
  color: #a0aec0;
  font-size: 14px;
}

.full-width {
  grid-column: 1 / -1;
}

.alert,
.quick-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.action-button {
  background-color: #ecc94b;
  color: #1a202c;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.inventory-table,
.customer-table,
.order-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.inventory-table th,
.inventory-table td,
.customer-table th,
.customer-table td,
.order-table th,
.order-table td {
  border: 1px solid #4a5568;
  padding: 8px;
  text-align: left;
}

.inventory-table th,
.customer-table th,
.order-table th {
  background-color: #2d3748;
  color: #ecc94b;
}

.light .inventory-table th,
.light .customer-table th,
.light .order-table th {
  background-color: #edf2f7;
  color: #2d3748;
}

.notifications-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.notification {
  background-color: #4a5568;
  color: white;
  padding: 10px 15px;
  border-radius: 4px;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  animation: slideIn 0.3s ease-out;
  cursor: pointer;
}

.light .notification {
  background-color: #e2e8f0;
  color: #2d3748;
}

.notification .close-button {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 0;
  margin-left: 10px;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@media (max-width: 768px) {
  .dashboard-grid,
  .insights-grid,
  .inventory-content,
  .customers-content,
  .sales-content,
  .orders-content {
    grid-template-columns: 1fr;
  }

  .quick-actions-grid {
    grid-template-columns: 1fr;
  }

  .tabs {
    flex-wrap: wrap;
  }

  .tab-button {
    flex-grow: 1;
    text-align: center;
  }
}

.table-container {
  max-height: 500px;
  overflow-y: auto;
  overflow-x: auto;
}

.events-table {
  width: 100%;
  border-collapse: collapse;
}

.events-table th {
  position: sticky;
  top: 0;
  background-color: var(--background-color);
  z-index: 1;
}

.events-table th,
.events-table td {
  padding: 8px;
  text-align: left;
  border: 1px solid var(--border-color);
}

.events-table td {
  white-space: pre-wrap;
  word-break: break-word;
}

.eye-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.eye-button:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.dark .eye-button {
  color: white;
}

.dark .eye-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Detail View Styles */
.detail-view {
  width: 100%;
  height: 100%;
}

.detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  gap: 20px;
  padding: 0 16px;
}

.detail-header h2 {
  margin: 0;
  font-size: 1.5rem;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
  font-weight: 500;
  border: none;
}

/* Light Mode Styles */
.light .back-button {
  background: #e0e7ff;
  color: #4f46e5;
}

.light .back-button:hover {
  background: #c7d2fe;
  transform: translateX(-3px);
}

.light .detail-header h2 {
  color: #1f2937;
}

.light .detail-table {
  background: #ffffff;
  border: 1px solid #e5e7eb;
}

.light .key-cell {
  background-color: #f3f4f6;
  color: #4b5563;
}

.light .value-cell {
  color: #1f2937;
}

.light .array-item {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
}

.light .sub-key {
  color: #4b5563;
}

.light .sub-value {
  color: #1f2937;
}

/* Dark Mode Styles */
.dark .back-button {
  background: #312e81;
  color: #e0e7ff;
}

.dark .back-button:hover {
  background: #3730a3;
  transform: translateX(-3px);
}

.dark .detail-header h2 {
  color: #f3f4f6;
}

.dark .detail-table {
  background: #1f2937;
  border: 1px solid #374151;
}

.dark .key-cell {
  background-color: #111827;
  color: #9ca3af;
  border-bottom: 1px solid #374151;
}

.dark .value-cell {
  color: #e5e7eb;
  border-bottom: 1px solid #374151;
}

.dark .array-item {
  background: #111827;
  border: 1px solid #374151;
}

.dark .sub-key {
  color: #9ca3af;
}

.dark .sub-value {
  color: #e5e7eb;
}

/* Common Styles */
.detail-table-wrapper {
  padding: 0 16px;
}

.detail-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 8px;
  overflow: hidden;
}

.key-cell {
  width: 25%;
  padding: 16px;
  font-weight: 600;
}

.value-cell {
  width: 75%;
  padding: 16px;
}

.array-value,
.object-value {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.array-item {
  padding: 12px;
  border-radius: 6px;
  margin: 4px 0;
}

.sub-item {
  display: flex;
  gap: 12px;
  padding: 4px 0;
}

.sub-key {
  font-weight: 500;
  min-width: 120px;
}

/* Hover Effects */
.detail-table tr:hover .key-cell {
  background-color: #374151;
  color: #e5e7eb;
}

.dark .detail-table tr:hover {
  background-color: #374151;
}

.light .detail-table tr:hover {
  background-color: #f9fafb;
}

/* Transitions */
.back-button,
.detail-table tr {
  transition: all 0.2s ease-in-out;
}

.empty-notifications,
.empty-table {
  text-align: center;
  padding: 20px;
  color: #666;
  font-style: italic;
}

.empty-table {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  margin: 20px 0;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.logout-button {
  background: none;
  border: none;
  cursor: pointer;
  color: inherit;
  padding: 5px;
  border-radius: 5px;
  transition: background-color 0.2s;
}

.logout-button:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.dark .logout-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Notification Permission Modal */
.notification-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: fadeIn 0.3s ease-out;
}

.notification-modal {
  background: white;
  border-radius: 16px;
  padding: 0;
  max-width: 480px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: slideInUp 0.3s ease-out;
  position: relative;
}

.dark .notification-modal {
  background: #2d3748;
  color: white;
}

.notification-modal-header {
  position: relative;
  padding: 16px 20px 0 20px;
}

.modal-close-button {
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  color: #718096;
  transition: all 0.2s ease;
}

.modal-close-button:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #2d3748;
}

.dark .modal-close-button {
  color: #a0aec0;
}

.dark .modal-close-button:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.notification-modal-content {
  padding: 20px 32px;
  text-align: center;
}

.modal-icon {
  margin-bottom: 16px;
  color: #3182ce;
}

.modal-icon.denied {
  color: #e53e3e;
}

.modal-icon.default {
  color: #38a169;
  animation: pulse 2s infinite;
}

.notification-modal-content h2 {
  margin: 0 0 12px 0;
  font-size: 24px;
  font-weight: 600;
  color: #2d3748;
}

.dark .notification-modal-content h2 {
  color: white;
}

.notification-modal-content p {
  margin: 0 0 20px 0;
  color: #4a5568;
  line-height: 1.6;
  font-size: 16px;
}

.dark .notification-modal-content p {
  color: #cbd5e0;
}

.notification-instructions {
  background: #f7fafc;
  border-radius: 8px;
  padding: 16px;
  margin: 20px 0;
  text-align: left;
}

.dark .notification-instructions {
  background: #4a5568;
}

.instruction-item {
  margin: 8px 0;
  color: #2d3748;
  font-size: 14px;
  line-height: 1.5;
}

.dark .instruction-item {
  color: #e2e8f0;
}

.notification-modal-actions {
  padding: 20px 32px 32px 32px;
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.modal-primary-button, .modal-secondary-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  min-width: 120px;
  justify-content: center;
}

.modal-primary-button {
  background: #3182ce;
  color: white;
}

.modal-primary-button:hover {
  background: #2c5aa0;
  transform: translateY(-1px);
}

.modal-secondary-button {
  background: #e2e8f0;
  color: #4a5568;
}

.modal-secondary-button:hover {
  background: #cbd5e0;
}

.dark .modal-secondary-button {
  background: #4a5568;
  color: #e2e8f0;
}

.dark .modal-secondary-button:hover {
  background: #718096;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}
