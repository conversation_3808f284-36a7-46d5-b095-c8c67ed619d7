.app {
  min-height: 100vh;
  padding: 0;
  transition: all 0.3s ease;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
}

.app.dark {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  color: #f8fafc;
}

.app.light {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
  color: #1e293b;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px 32px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dark .header {
  background: rgba(15, 23, 42, 0.8);
  border: 1px solid rgba(148, 163, 184, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.light .header {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(226, 232, 240, 0.5);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
}

.logo {
  display: flex;
  align-items: center;
  gap: 16px;
}

.logo-circle {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
  position: relative;
}

.logo-circle::before {
  content: '⚡';
  font-size: 24px;
  color: white;
}

.logo h1 {
  font-size: 28px;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.theme-toggle, .test-notification-button, .logout-button {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  padding: 12px;
  border-radius: 12px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  color: inherit;
}

.theme-toggle:hover, .test-notification-button:hover, .logout-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.dark .theme-toggle, .dark .test-notification-button, .dark .logout-button {
  background: rgba(148, 163, 184, 0.1);
  border: 1px solid rgba(148, 163, 184, 0.2);
  color: #f8fafc;
}

.dark .theme-toggle:hover, .dark .test-notification-button:hover, .dark .logout-button:hover {
  background: rgba(148, 163, 184, 0.2);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.light .theme-toggle, .light .test-notification-button, .light .logout-button {
  background: rgba(226, 232, 240, 0.8);
  border: 1px solid rgba(203, 213, 225, 0.5);
  color: #1e293b;
}

.light .theme-toggle:hover, .light .test-notification-button:hover, .light .logout-button:hover {
  background: rgba(203, 213, 225, 0.9);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
}

.test-notification-button {
  font-size: 18px;
  position: relative;
}

.test-notification-button::after {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.tabs {
  display: flex;
  background-color: #2d3748;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}

.tab-button {
  padding: 10px 20px;
  border: none;
  background: none;
  color: #a0aec0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.tab-button.active {
  background-color: #ecc94b;
  color: #1a202c;
}

.dashboard-grid,
.insights-grid,
.inventory-content,
.customers-content,
.sales-content,
.orders-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 32px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.dark .card {
  background: rgba(15, 23, 42, 0.8);
  border: 1px solid rgba(148, 163, 184, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.light .card {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(226, 232, 240, 0.5);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
}

.card h3 {
  margin-top: 0;
  margin-bottom: 10px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #4299e1;
  margin: 10px 0;
}

.card-subtitle {
  color: #a0aec0;
  font-size: 14px;
}

.full-width {
  grid-column: 1 / -1;
}

.alert,
.quick-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.action-button {
  background-color: #ecc94b;
  color: #1a202c;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.inventory-table,
.customer-table,
.order-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.inventory-table th,
.inventory-table td,
.customer-table th,
.customer-table td,
.order-table th,
.order-table td {
  border: 1px solid #4a5568;
  padding: 8px;
  text-align: left;
}

.inventory-table th,
.customer-table th,
.order-table th {
  background-color: #2d3748;
  color: #ecc94b;
}

.light .inventory-table th,
.light .customer-table th,
.light .order-table th {
  background-color: #edf2f7;
  color: #2d3748;
}

.notifications-container {
  position: fixed;
  top: 24px;
  right: 24px;
  z-index: 1000;
  max-width: 400px;
}

.notification {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  color: white;
  padding: 20px 24px;
  border-radius: 16px;
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.notification::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
}

.notification:hover {
  transform: translateX(-4px) scale(1.02);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
}

.dark .notification {
  background: rgba(15, 23, 42, 0.9);
  border: 1px solid rgba(148, 163, 184, 0.2);
  color: #f8fafc;
}

.light .notification {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(226, 232, 240, 0.5);
  color: #1e293b;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.notification .close-button {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: inherit;
  cursor: pointer;
  padding: 8px;
  margin-left: 16px;
  border-radius: 8px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.notification .close-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.notification-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.notification-message {
  font-weight: 500;
  font-size: 14px;
  line-height: 1.4;
}

.notification-time {
  font-size: 12px;
  opacity: 0.7;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%) scale(0.9);
    opacity: 0;
  }
  to {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* Additional Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .container {
    padding: 16px;
  }

  .header {
    padding: 20px 24px;
    margin-bottom: 24px;
  }

  .card {
    padding: 24px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 12px;
  }

  .header {
    flex-direction: column;
    gap: 20px;
    padding: 20px;
    text-align: center;
  }

  .header-actions {
    justify-content: center;
  }

  .logo h1 {
    font-size: 24px;
  }

  .card {
    padding: 20px;
    border-radius: 16px;
  }

  .events-table th,
  .events-table td {
    padding: 12px 16px;
    font-size: 13px;
  }

  .detail-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .detail-header h2 {
    font-size: 24px;
  }

  .notifications-container {
    left: 12px;
    right: 12px;
    top: 12px;
    max-width: none;
  }

  .notification {
    padding: 16px 20px;
  }
}

@media (max-width: 480px) {
  .key-cell {
    width: 35%;
    padding: 16px;
    font-size: 12px;
  }

  .value-cell {
    width: 65%;
    padding: 16px;
    font-size: 14px;
  }

  .back-button {
    padding: 12px 20px;
    font-size: 14px;
  }

  .theme-toggle, .test-notification-button, .logout-button {
    padding: 10px;
  }
}

.table-container {
  max-height: 600px;
  overflow-y: auto;
  overflow-x: auto;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
}

.events-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-size: 14px;
}

.events-table th {
  position: sticky;
  top: 0;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: white;
  z-index: 10;
  padding: 16px 20px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 12px;
  border: none;
}

.events-table th:first-child {
  border-top-left-radius: 16px;
}

.events-table th:last-child {
  border-top-right-radius: 16px;
}

.events-table td {
  padding: 16px 20px;
  border: none;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  white-space: pre-wrap;
  word-break: break-word;
  transition: all 0.2s ease;
}

.events-table tbody tr {
  transition: all 0.2s ease;
}

.events-table tbody tr:hover {
  background: rgba(59, 130, 246, 0.1);
  transform: scale(1.01);
}

.dark .events-table td {
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  color: #e2e8f0;
}

.light .events-table td {
  border-bottom: 1px solid rgba(226, 232, 240, 0.3);
  color: #374151;
}

.dark .events-table tbody tr:hover {
  background: rgba(59, 130, 246, 0.15);
}

.light .events-table tbody tr:hover {
  background: rgba(59, 130, 246, 0.08);
}

.eye-button {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border: none;
  cursor: pointer;
  padding: 10px;
  border-radius: 12px;
  transition: all 0.3s ease;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.eye-button:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.eye-button:active {
  transform: translateY(0) scale(0.95);
}

/* Detail View Styles */
.detail-view {
  width: 100%;
  height: 100%;
  animation: fadeInUp 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 32px;
  gap: 24px;
  padding: 0 8px;
}

.detail-header h2 {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
  font-weight: 600;
  border: none;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.back-button:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
}

.back-button:active {
  transform: translateY(0) scale(0.98);
}

/* Light Mode Styles */
.light .back-button {
  background: #e0e7ff;
  color: #4f46e5;
}

.light .back-button:hover {
  background: #c7d2fe;
  transform: translateX(-3px);
}

.light .detail-header h2 {
  color: #1f2937;
}

.light .detail-table {
  background: #ffffff;
  border: 1px solid #e5e7eb;
}

.light .key-cell {
  background-color: #f3f4f6;
  color: #4b5563;
}

.light .value-cell {
  color: #1f2937;
}

.light .array-item {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
}

.light .sub-key {
  color: #4b5563;
}

.light .sub-value {
  color: #1f2937;
}

/* Dark Mode Styles */
.dark .back-button {
  background: #312e81;
  color: #e0e7ff;
}

.dark .back-button:hover {
  background: #3730a3;
  transform: translateX(-3px);
}

.dark .detail-header h2 {
  color: #f3f4f6;
}

.dark .detail-table {
  background: #1f2937;
  border: 1px solid #374151;
}

.dark .key-cell {
  background-color: #111827;
  color: #9ca3af;
  border-bottom: 1px solid #374151;
}

.dark .value-cell {
  color: #e5e7eb;
  border-bottom: 1px solid #374151;
}

.dark .array-item {
  background: #111827;
  border: 1px solid #374151;
}

.dark .sub-key {
  color: #9ca3af;
}

.dark .sub-value {
  color: #e5e7eb;
}

/* Common Styles */
.detail-table-wrapper {
  padding: 0 8px;
  border-radius: 20px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
}

.detail-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.key-cell {
  width: 30%;
  padding: 20px 24px;
  font-weight: 700;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  color: white;
  border: none;
}

.value-cell {
  width: 70%;
  padding: 20px 24px;
  font-size: 15px;
  line-height: 1.6;
  border: none;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.dark .value-cell {
  background: rgba(15, 23, 42, 0.5);
  color: #e2e8f0;
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
}

.light .value-cell {
  background: rgba(255, 255, 255, 0.8);
  color: #374151;
  border-bottom: 1px solid rgba(226, 232, 240, 0.3);
}

.array-value,
.object-value {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.array-item {
  padding: 16px 20px;
  border-radius: 12px;
  margin: 8px 0;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  transition: all 0.2s ease;
}

.array-item:hover {
  background: rgba(59, 130, 246, 0.15);
  transform: translateX(4px);
}

.sub-item {
  display: flex;
  gap: 16px;
  padding: 8px 0;
  align-items: flex-start;
}

.sub-key {
  font-weight: 600;
  min-width: 140px;
  color: #3b82f6;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.sub-value {
  flex: 1;
  word-break: break-word;
}

/* Hover Effects */
.detail-table tr:hover .key-cell {
  background-color: #374151;
  color: #e5e7eb;
}

.dark .detail-table tr:hover {
  background-color: #374151;
}

.light .detail-table tr:hover {
  background-color: #f9fafb;
}

/* Transitions */
.back-button,
.detail-table tr {
  transition: all 0.2s ease-in-out;
}

.empty-notifications,
.empty-table {
  text-align: center;
  padding: 60px 40px;
  color: #94a3b8;
  font-size: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.empty-table {
  background: rgba(148, 163, 184, 0.1);
  border-radius: 20px;
  margin: 20px 0;
  border: 2px dashed rgba(148, 163, 184, 0.3);
}

.empty-table::before {
  content: '📊';
  font-size: 48px;
  display: block;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-notifications::before {
  content: '🔔';
  font-size: 48px;
  display: block;
  margin-bottom: 16px;
  opacity: 0.5;
}

.dark .empty-notifications,
.dark .empty-table {
  color: #64748b;
}

.light .empty-notifications,
.light .empty-table {
  color: #64748b;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.logout-button {
  background: none;
  border: none;
  cursor: pointer;
  color: inherit;
  padding: 5px;
  border-radius: 5px;
  transition: background-color 0.2s;
}

.logout-button:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.dark .logout-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Notification Permission Modal */
.notification-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: fadeIn 0.3s ease-out;
}

.notification-modal {
  background: white;
  border-radius: 16px;
  padding: 0;
  max-width: 480px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: slideInUp 0.3s ease-out;
  position: relative;
}

.dark .notification-modal {
  background: #2d3748;
  color: white;
}

.notification-modal-header {
  position: relative;
  padding: 16px 20px 0 20px;
}

.modal-close-button {
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  color: #718096;
  transition: all 0.2s ease;
}

.modal-close-button:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #2d3748;
}

.dark .modal-close-button {
  color: #a0aec0;
}

.dark .modal-close-button:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.notification-modal-content {
  padding: 20px 32px;
  text-align: center;
}

.modal-icon {
  margin-bottom: 16px;
  color: #3182ce;
}

.modal-icon.denied {
  color: #e53e3e;
}

.modal-icon.default {
  color: #38a169;
  animation: pulse 2s infinite;
}

.notification-modal-content h2 {
  margin: 0 0 12px 0;
  font-size: 24px;
  font-weight: 600;
  color: #2d3748;
}

.dark .notification-modal-content h2 {
  color: white;
}

.notification-modal-content p {
  margin: 0 0 20px 0;
  color: #4a5568;
  line-height: 1.6;
  font-size: 16px;
}

.dark .notification-modal-content p {
  color: #cbd5e0;
}

.notification-instructions {
  background: #f7fafc;
  border-radius: 8px;
  padding: 16px;
  margin: 20px 0;
  text-align: left;
}

.dark .notification-instructions {
  background: #4a5568;
}

.instruction-item {
  margin: 8px 0;
  color: #2d3748;
  font-size: 14px;
  line-height: 1.5;
}

.dark .instruction-item {
  color: #e2e8f0;
}

.notification-modal-actions {
  padding: 20px 32px 32px 32px;
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.modal-primary-button, .modal-secondary-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  min-width: 120px;
  justify-content: center;
}

.modal-primary-button {
  background: #3182ce;
  color: white;
}

.modal-primary-button:hover {
  background: #2c5aa0;
  transform: translateY(-1px);
}

.modal-secondary-button {
  background: #e2e8f0;
  color: #4a5568;
}

.modal-secondary-button:hover {
  background: #cbd5e0;
}

.dark .modal-secondary-button {
  background: #4a5568;
  color: #e2e8f0;
}

.dark .modal-secondary-button:hover {
  background: #718096;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}
