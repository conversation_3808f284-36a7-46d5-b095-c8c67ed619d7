.app {
  min-height: 100vh;
  padding: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
  font-weight: 400;
  line-height: 1.6;
}

.app.dark {
  background: #0a0a0b;
  background-image:
    radial-gradient(circle at 25% 25%, #1a1a2e 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, #16213e 0%, transparent 50%);
  color: #ffffff;
}

.app.light {
  background: #fafbfc;
  background-image:
    radial-gradient(circle at 25% 25%, #f0f4f8 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, #e8f2ff 0%, transparent 50%);
  color: #1a202c;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px 28px;
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(24px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.1),
    0 1px 2px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
}

.dark .header {
  background: rgba(26, 32, 44, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.06);
  box-shadow:
    0 4px 6px rgba(0, 0, 0, 0.3),
    0 1px 3px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.light .header {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.1),
    0 1px 2px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-circle {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 2px 4px rgba(37, 99, 235, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
}

.logo-circle::before {
  content: '📊';
  font-size: 18px;
  filter: brightness(1.2);
}

.logo h1 {
  font-size: 22px;
  font-weight: 600;
  margin: 0;
  color: #1a202c;
  letter-spacing: -0.025em;
}

.dark .logo h1 {
  color: #ffffff;
}

.light .logo h1 {
  color: #1a202c;
}

.header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.theme-toggle, .logout-button {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  padding: 10px;
  border-radius: 8px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: inherit;
  width: 36px;
  height: 36px;
}

.theme-toggle:hover, .logout-button:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.dark .theme-toggle, .dark .logout-button {
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #e2e8f0;
}

.dark .theme-toggle:hover, .dark .logout-button:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.2);
}

.light .theme-toggle, .light .logout-button {
  border: 1px solid rgba(0, 0, 0, 0.1);
  color: #4a5568;
}

.light .theme-toggle:hover, .light .logout-button:hover {
  background: rgba(0, 0, 0, 0.04);
  border-color: rgba(0, 0, 0, 0.15);
}

.logout-button {
  color: #ef4444;
  border-color: rgba(239, 68, 68, 0.2);
}

.logout-button:hover {
  background: rgba(239, 68, 68, 0.1);
  border-color: rgba(239, 68, 68, 0.3);
}

.tabs {
  display: flex;
  background-color: #2d3748;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}

.tab-button {
  padding: 10px 20px;
  border: none;
  background: none;
  color: #a0aec0;
  cursor: pointer;
  transition: background-color 0.3s;
}

.tab-button.active {
  background-color: #ecc94b;
  color: #1a202c;
}

.dashboard-grid,
.insights-grid,
.inventory-content,
.customers-content,
.sales-content,
.orders-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.card {
  background: rgba(255, 255, 255, 0.02);
  backdrop-filter: blur(24px);
  border-radius: 12px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.1),
    0 1px 2px rgba(0, 0, 0, 0.06);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.card:hover {
  transform: translateY(-1px);
  box-shadow:
    0 4px 6px rgba(0, 0, 0, 0.1),
    0 2px 4px rgba(0, 0, 0, 0.06);
}

.dark .card {
  background: rgba(26, 32, 44, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.06);
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.3),
    0 1px 2px rgba(0, 0, 0, 0.2);
}

.light .card {
  background: rgba(255, 255, 255, 0.7);
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.1),
    0 1px 2px rgba(0, 0, 0, 0.06);
}

.card h3 {
  margin-top: 0;
  margin-bottom: 10px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #4299e1;
  margin: 10px 0;
}

.card-subtitle {
  color: #a0aec0;
  font-size: 14px;
}

.full-width {
  grid-column: 1 / -1;
}

.alert,
.quick-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.action-button {
  background-color: #ecc94b;
  color: #1a202c;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.inventory-table,
.customer-table,
.order-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
}

.inventory-table th,
.inventory-table td,
.customer-table th,
.customer-table td,
.order-table th,
.order-table td {
  border: 1px solid #4a5568;
  padding: 8px;
  text-align: left;
}

.inventory-table th,
.customer-table th,
.order-table th {
  background-color: #2d3748;
  color: #ecc94b;
}

.light .inventory-table th,
.light .customer-table th,
.light .order-table th {
  background-color: #edf2f7;
  color: #2d3748;
}

.notifications-container {
  position: fixed;
  top: 16px;
  right: 16px;
  z-index: 1000;
  max-width: 360px;
}

.notification {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(16px);
  color: #1a202c;
  padding: 16px 20px;
  border-radius: 8px;
  margin-bottom: 8px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow:
    0 4px 6px rgba(0, 0, 0, 0.1),
    0 1px 3px rgba(0, 0, 0, 0.08);
  animation: slideInRight 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.notification::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: #2563eb;
}

.notification:hover {
  transform: translateY(-1px);
  box-shadow:
    0 6px 8px rgba(0, 0, 0, 0.12),
    0 2px 4px rgba(0, 0, 0, 0.08);
}

.dark .notification {
  background: rgba(26, 32, 44, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.08);
  color: #ffffff;
  box-shadow:
    0 4px 6px rgba(0, 0, 0, 0.3),
    0 1px 3px rgba(0, 0, 0, 0.2);
}

.light .notification {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(0, 0, 0, 0.08);
  color: #1a202c;
}

.notification .close-button {
  background: transparent;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  margin-left: 12px;
  border-radius: 4px;
  transition: all 0.15s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.notification .close-button:hover {
  background: rgba(107, 114, 128, 0.1);
  color: #374151;
}

.dark .notification .close-button {
  color: #9ca3af;
}

.dark .notification .close-button:hover {
  background: rgba(156, 163, 175, 0.1);
  color: #e5e7eb;
}

.notification-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.notification-message {
  font-weight: 500;
  font-size: 13px;
  line-height: 1.4;
  margin: 0;
}

.notification-time {
  font-size: 11px;
  opacity: 0.6;
  font-weight: 400;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%) scale(0.9);
    opacity: 0;
  }
  to {
    transform: translateX(0) scale(1);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* Professional Scrollbar Styling */
.table-container::-webkit-scrollbar,
.detail-table-wrapper::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.table-container::-webkit-scrollbar-track,
.detail-table-wrapper::-webkit-scrollbar-track {
  background: transparent;
}

.table-container::-webkit-scrollbar-thumb,
.detail-table-wrapper::-webkit-scrollbar-thumb {
  background: rgba(107, 114, 128, 0.3);
  border-radius: 3px;
}

.table-container::-webkit-scrollbar-thumb:hover,
.detail-table-wrapper::-webkit-scrollbar-thumb:hover {
  background: rgba(107, 114, 128, 0.5);
}

.dark .table-container::-webkit-scrollbar-thumb,
.dark .detail-table-wrapper::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.3);
}

.dark .table-container::-webkit-scrollbar-thumb:hover,
.dark .detail-table-wrapper::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.5);
}

/* Additional Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .container {
    padding: 16px;
  }

  .header {
    padding: 20px 24px;
    margin-bottom: 24px;
  }

  .card {
    padding: 24px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 12px;
  }

  .header {
    flex-direction: row;
    gap: 20px;
    padding: 20px;
    text-align: center;
  }

  .header-actions {
    justify-content: center;
  }

  .logo h1 {
    font-size: 24px;
  }

  .card {
    padding: 20px;
    border-radius: 16px;
  }

  .events-table th,
  .events-table td {
    padding: 12px 16px;
    font-size: 13px;
  }

  .detail-header {
    flex-direction: row;
    align-items: center;
    gap: 16px;
  }

  .detail-header h2 {
    font-size: 24px;
  }

  .notifications-container {
    left: 12px;
    right: 12px;
    top: 12px;
    max-width: none;
  }

  .notification {
    padding: 16px 20px;
  }
}

@media (max-width: 480px) {
  .key-cell {
    width: 35%;
    padding: 16px;
    font-size: 12px;
  }

  .value-cell {
    width: 65%;
    padding: 16px;
    font-size: 14px;
  }

  .back-button {
    padding: 12px 20px;
    font-size: 14px;
  }

  .theme-toggle, .test-notification-button, .logout-button {
    padding: 10px;
  }
}

.table-container {
  max-height: 500px;
  overflow-y: auto;
  overflow-x: auto;
  border-radius: 8px;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.dark .table-container {
  border: 1px solid rgba(255, 255, 255, 0.06);
}

.light .table-container {
  border: 1px solid rgba(0, 0, 0, 0.06);
}

.events-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  font-size: 13px;
}

.events-table th {
  position: sticky;
  top: 0;
  background: #f8fafc;
  color: #374151;
  z-index: 10;
  padding: 12px 16px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  font-size: 11px;
  border: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.dark .events-table th {
  background: #1a202c;
  color: #e2e8f0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.06);
}

.events-table td {
  padding: 12px 16px;
  border: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.04);
  white-space: pre-wrap;
  word-break: break-word;
  transition: background-color 0.15s ease;
  font-size: 13px;
  line-height: 1.5;
}

.events-table tbody tr:hover {
  background: rgba(59, 130, 246, 0.04);
}

.dark .events-table td {
  border-bottom: 1px solid rgba(255, 255, 255, 0.04);
  color: #e2e8f0;
}

.light .events-table td {
  border-bottom: 1px solid rgba(0, 0, 0, 0.04);
  color: #374151;
}

.dark .events-table tbody tr:hover {
  background: rgba(59, 130, 246, 0.08);
}

.light .events-table tbody tr:hover {
  background: rgba(59, 130, 246, 0.04);
}

.eye-button {
  background: #2563eb;
  border: none;
  cursor: pointer;
  padding: 6px;
  border-radius: 6px;
  transition: all 0.15s ease;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  width: 28px;
  height: 28px;
}

.eye-button:hover {
  background: #1d4ed8;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.eye-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Detail View Styles */
.detail-view {
  width: 100%;
  height: 100%;
  animation: fadeInUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.detail-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
  padding: 0 4px;
}

.detail-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  letter-spacing: -0.025em;
}

.dark .detail-header h2 {
  color: #ffffff;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid rgba(37, 99, 235, 0.2);
  background: #2563eb;
  color: white;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.back-button:hover {
  background: #1d4ed8;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.back-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Light Mode Styles */
.light .back-button {
  background: #e0e7ff;
  color: #4f46e5;
}

.light .back-button:hover {
  background: #c7d2fe;
  transform: translateX(-3px);
}

.light .detail-header h2 {
  color: #1f2937;
}

.light .detail-table {
  background: #ffffff;
  border: 1px solid #e5e7eb;
}

.light .key-cell {
  background-color: #f3f4f6;
  color: #4b5563;
}

.light .value-cell {
  color: #1f2937;
}

.light .array-item {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
}

.light .sub-key {
  color: #4b5563;
}

.light .sub-value {
  color: #1f2937;
}

/* Dark Mode Styles */
.dark .back-button {
  background: #312e81;
  color: #e0e7ff;
}

.dark .back-button:hover {
  background: #3730a3;
  transform: translateX(-3px);
}

.dark .detail-header h2 {
  color: #f3f4f6;
}

.dark .detail-table {
  background: #1f2937;
  border: 1px solid #374151;
}

.dark .key-cell {
  background-color: #111827;
  color: #9ca3af;
  border-bottom: 1px solid #374151;
}

.dark .value-cell {
  color: #e5e7eb;
  border-bottom: 1px solid #374151;
}

.dark .array-item {
  background: #111827;
  border: 1px solid #374151;
}

.dark .sub-key {
  color: #9ca3af;
}

.dark .sub-value {
  color: #e5e7eb;
}

/* Common Styles */
.detail-table-wrapper {
  padding: 0 4px;
  border-radius: 8px;
  overflow: hidden;
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.08);
}

.dark .detail-table-wrapper {
  border: 1px solid rgba(255, 255, 255, 0.06);
}

.light .detail-table-wrapper {
  border: 1px solid rgba(0, 0, 0, 0.06);
}

.detail-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 8px;
  overflow: hidden;
}

.key-cell {
  width: 25%;
  padding: 12px 16px;
  font-weight: 600;
  font-size: 11px;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  background: #f8fafc;
  color: #374151;
  border: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.dark .key-cell {
  background: #1a202c;
  color: #e2e8f0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.06);
}

.value-cell {
  width: 75%;
  padding: 12px 16px;
  font-size: 13px;
  line-height: 1.5;
  border: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.04);
  background: rgba(255, 255, 255, 0.5);
  color: #374151;
}

.dark .value-cell {
  background: rgba(26, 32, 44, 0.3);
  color: #e2e8f0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.04);
}

.light .value-cell {
  background: rgba(255, 255, 255, 0.5);
  color: #374151;
  border-bottom: 1px solid rgba(0, 0, 0, 0.04);
}

.array-value,
.object-value {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.array-item {
  padding: 16px 20px;
  border-radius: 12px;
  margin: 8px 0;
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  transition: all 0.2s ease;
}

.array-item:hover {
  background: rgba(59, 130, 246, 0.15);
  transform: translateX(4px);
}

.sub-item {
  display: flex;
  gap: 16px;
  padding: 8px 0;
  align-items: flex-start;
}

.sub-key {
  font-weight: 600;
  min-width: 140px;
  color: #3b82f6;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.sub-value {
  flex: 1;
  word-break: break-word;
}

/* Hover Effects */
.detail-table tr:hover .key-cell {
  background-color: #374151;
  color: #e5e7eb;
}

.dark .detail-table tr:hover {
  background-color: #374151;
}

.light .detail-table tr:hover {
  background-color: #f9fafb;
}

/* Transitions */
.back-button,
.detail-table tr {
  transition: all 0.2s ease-in-out;
}

.empty-notifications,
.empty-table {
  text-align: center;
  padding: 40px 32px;
  color: #6b7280;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.empty-table {
  background: rgba(107, 114, 128, 0.05);
  border-radius: 8px;
  margin: 16px 0;
  border: 1px dashed rgba(107, 114, 128, 0.2);
}

.empty-table::before {
  content: '📊';
  font-size: 32px;
  display: block;
  margin-bottom: 8px;
  opacity: 0.4;
}

.empty-notifications::before {
  content: '🔔';
  font-size: 32px;
  display: block;
  margin-bottom: 8px;
  opacity: 0.4;
}

.dark .empty-notifications,
.dark .empty-table {
  color: #9ca3af;
  background: rgba(156, 163, 175, 0.05);
  border-color: rgba(156, 163, 175, 0.2);
}

.light .empty-notifications,
.light .empty-table {
  color: #6b7280;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.logout-button {
  background: none;
  border: none;
  cursor: pointer;
  color: inherit;
  padding: 5px;
  border-radius: 5px;
  transition: background-color 0.2s;
}

.logout-button:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.dark .logout-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Notification Permission Modal */
.notification-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: fadeIn 0.3s ease-out;
}

.notification-modal {
  background: white;
  border-radius: 16px;
  padding: 0;
  max-width: 480px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: slideInUp 0.3s ease-out;
  position: relative;
}

.dark .notification-modal {
  background: #2d3748;
  color: white;
}

.notification-modal-header {
  position: relative;
  padding: 16px 20px 0 20px;
}

.modal-close-button {
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  color: #718096;
  transition: all 0.2s ease;
}

.modal-close-button:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #2d3748;
}

.dark .modal-close-button {
  color: #a0aec0;
}

.dark .modal-close-button:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.notification-modal-content {
  padding: 20px 32px;
  text-align: center;
}

.modal-icon {
  margin-bottom: 16px;
  color: #3182ce;
}

.modal-icon.denied {
  color: #e53e3e;
}

.modal-icon.default {
  color: #38a169;
  animation: pulse 2s infinite;
}

.notification-modal-content h2 {
  margin: 0 0 12px 0;
  font-size: 24px;
  font-weight: 600;
  color: #2d3748;
}

.dark .notification-modal-content h2 {
  color: white;
}

.notification-modal-content p {
  margin: 0 0 20px 0;
  color: #4a5568;
  line-height: 1.6;
  font-size: 16px;
}

.dark .notification-modal-content p {
  color: #cbd5e0;
}

.notification-instructions {
  background: #f7fafc;
  border-radius: 8px;
  padding: 16px;
  margin: 20px 0;
  text-align: left;
}

.dark .notification-instructions {
  background: #4a5568;
}

.instruction-item {
  margin: 8px 0;
  color: #2d3748;
  font-size: 14px;
  line-height: 1.5;
}

.dark .instruction-item {
  color: #e2e8f0;
}

.notification-modal-actions {
  padding: 20px 32px 32px 32px;
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.modal-primary-button, .modal-secondary-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  min-width: 120px;
  justify-content: center;
}

.modal-primary-button {
  background: #3182ce;
  color: white;
}

.modal-primary-button:hover {
  background: #2c5aa0;
  transform: translateY(-1px);
}

.modal-secondary-button {
  background: #e2e8f0;
  color: #4a5568;
}

.modal-secondary-button:hover {
  background: #cbd5e0;
}

.dark .modal-secondary-button {
  background: #4a5568;
  color: #e2e8f0;
}

.dark .modal-secondary-button:hover {
  background: #718096;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}
