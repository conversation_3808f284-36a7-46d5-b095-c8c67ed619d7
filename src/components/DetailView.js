import React from "react";
import { ArrowLeft } from "lucide-react";

const formatValue = (value) => {
  if (Array.isArray(value)) {
    return (
      <div className="array-value">
        {value.map((item, index) => (
          <div key={index} className="array-item">
            {typeof item === "object"
              ? Object.entries(item).map(([subKey, subValue]) => (
                  <div key={subKey} className="sub-item">
                    <span className="sub-key">{formatKey(subKey)}:</span>
                    <span className="sub-value">{formatValue(subValue)}</span>
                  </div>
                ))
              : String(item)}
          </div>
        ))}
      </div>
    );
  }

  if (value && typeof value === "object") {
    return (
      <div className="object-value">
        {Object.entries(value).map(([subKey, subValue]) => (
          <div key={subKey} className="sub-item">
            <span className="sub-key">{subKey}:</span>
            <span className="sub-value">{formatValue(subValue)}</span>
          </div>
        ))}
      </div>
    );
  }

  return String(value);
};

const formatKey = (key) => {
  return key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, " ");
};

const DetailView = ({ data, onBack }) => {
  const entries = Object.entries(data || {});

  return (
    <div className="detail-view">
      <div className="detail-header">
        <button onClick={onBack} className="back-button">
          <ArrowLeft size={24} />
          <span>Back</span>
        </button>
        <h2>Detailed Information</h2>
      </div>
      <div className="">
        <table className="detail-table">
          <tbody>
            {entries.map(([key, value]) =>
              // Skip rendering if the key is "Timestamp"
              key === "timestamp" ? null : (
                <tr key={key}>
                  <td className="key-cell">{formatKey(key)}</td>
                  <td className="value-cell">{formatValue(value)}</td>
                </tr>
              )
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default DetailView;
