import React, { useState, useEffect, useCallback, memo } from "react";
import { Sun, Moon, X, Eye, LogOut } from "lucide-react";
import DetailView from "./DetailView";
import { useNavigate } from "react-router-dom";
import "./Dashboard.css";
import { API_BASE_URL, BC_CREDENTIALS, APP_NAME } from "../config/const";
import { eventService } from "../service/api";

// Memoized Notification component
const Notification = memo(
  ({ message, id, timestamp, onClose, onClick, eventId }) => (
    <div
      className="notification"
      onClick={() => onClick({ id, message, timestamp, eventId })}
    >
      <span>{message}</span>
      <button
        onClick={(e) => {
          e.stopPropagation();
          onClose({ id, message, timestamp, eventId });
        }}
        className="close-button"
      >
        <X size={16} />
      </button>
    </div>
  )
);

const TABLE_HEADERS = ["ID", "Message", "Topic", "Actions"];

const Dashboard = () => {
  const navigate = useNavigate();
  const [isDarkMode, setIsDarkMode] = useState(true);
  const [notifications, setNotifications] = useState([]);
  const [tableData, setTableData] = useState([]);
  const [selectedData, setSelectedData] = useState(null);
  const [isDetailView, setIsDetailView] = useState(false);

  const toggleTheme = useCallback(() => setIsDarkMode((prev) => !prev), []);

  const handleViewData = useCallback((data) => {
    const storedCredentials = JSON.parse(localStorage.getItem(BC_CREDENTIALS));
    const email = storedCredentials?.email;
    if (data.viewed_by) {
      if (Array.isArray(data.viewed_by) && !data.viewed_by.includes(email)) {
        updateViewCount(data.eventId);
      }
    } else {
      updateViewCount(data.eventId);
    }
    setSelectedData(data);
    setIsDetailView(true);
  }, []);

  const handleBackToList = useCallback(() => {
    setIsDetailView(false);
    setSelectedData(null);
  }, []);

  const updateViewCount = async (id) => {
    try {
      const storedCredentials = JSON.parse(
        localStorage.getItem(BC_CREDENTIALS)
      );
      const email = storedCredentials?.email;
      const store = storedCredentials?.store;
      if (email && store) {
        const response = await eventService.updateViewCount({
          email: email,
          id: id,
          store_name: store,
        });
        if (response.status === 200) {
        }
      }
    } catch (error) {
      // console.log("error",error)
    }
  };

  const removeNotification = useCallback(async (eventId, notificationData) => {
    const storedCredentials = JSON.parse(localStorage.getItem(BC_CREDENTIALS));
    const email = storedCredentials?.email;
    if (
      notificationData.viewed_by &&
      Array.isArray(notificationData.viewed_by)
    ) {
      if (notificationData.viewed_by.includes(email)) {
        setNotifications((prev) =>
          prev.filter((notification) => notification.eventId !== eventId)
        );
        return;
      }
    }
    await updateViewCount(notificationData.eventId);
    setNotifications((prev) =>
      prev.filter((notification) => notification.eventId !== eventId)
    );
  }, []);

  const handleNotificationClick = useCallback(
    (notificationData) => {
      const tableEntry = tableData.find(
        (entry) => entry.id === notificationData.id
      );
      if (tableEntry) {
        handleViewData(tableEntry.data);
        removeNotification(notificationData.eventId, notificationData);
      }
    },
    [tableData, handleViewData, removeNotification]
  );

  const handleLogout = useCallback(() => {
    localStorage.removeItem(BC_CREDENTIALS);
    navigate("/login");
    // You might want to add additional logout logic here
  }, [navigate]);

  useEffect(() => {
    const fetchEvents = async () => {
      try {
        const storedCredentials = JSON.parse(localStorage.getItem(BC_CREDENTIALS));
        const email = storedCredentials?.email;
        const store = storedCredentials?.store;
        if (email && store) {
          const response = await eventService.getEvents({ email, store_name: store });
          if (response.status === 200) {
          const data = response?.data?.data?.data || [];
            setTableData(data);
          
          }
        }
      } catch (error) {
        console.error("Error fetching events:", error);
      }
    };

    fetchEvents();
  }, []);


  useEffect(() => {
    const eventSource = new EventSource(`${API_BASE_URL}/events`);
    const handleMessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        const actualData = Object.values(data)[0] || data;
        const storedCredentials = JSON.parse(
          localStorage.getItem(BC_CREDENTIALS)
        );
        if (!storedCredentials?.email) {
          return;
        }
        let checkEmailArray = [];
        if (typeof actualData?.check_email === "string") {
          checkEmailArray = actualData.check_email
            .split(",")
            .map((email) => email.trim());
        } else if (Array.isArray(actualData?.check_email)) {
          checkEmailArray = actualData.check_email;
        } else {
          console.warn(
            "check_email is not a string or an array:",
            actualData?.check_email
          );
          return;
        }

        // Skip if check_email field is missing or does not contain the stored email
        if (!checkEmailArray.includes(storedCredentials.email)) {
          return;
        }
        // Remove the check_email from the payload before displaying it
        const { check_email, ...cleanedData } = actualData;

        const message =
          cleanedData?.message ||
          (typeof cleanedData === "object"
            ? Object.values(cleanedData)[0]?.message ||
              JSON.stringify(cleanedData)
            : cleanedData);

        // Skip if message contains "connection"
        if (message.toLowerCase().includes("connection")) {
          return;
        }

        const timestamp = new Date().toLocaleTimeString();
        const id = Date.now();

        // Add the cleaned data to the notifications and table data
        setNotifications((prev) => [
          ...prev,
          { id, message, timestamp, eventId: actualData.eventId },
        ]);
        setTableData((prev) => [...prev, { id, timestamp, data: cleanedData }]);
      } catch (error) {
        //  console.error("Error processing event:", error);
      }
    };

    eventSource.onmessage = handleMessage;
    eventSource.onerror = (error) =>
      console.error("SSE connection error:", error);
    return () => eventSource.close();
  }, []);

  return (
    <div className={`app ${isDarkMode ? "dark" : "light"}`}>
      <div className="container">
        <div className="header">
          <div className="logo">
            <div className="logo-circle"></div>
            <h1>{APP_NAME}</h1>
          </div>
          <div className="header-actions">
            <button onClick={toggleTheme} className="theme-toggle">
              {isDarkMode ? <Sun size={24} /> : <Moon size={24} />}
            </button>
            <button onClick={handleLogout} className="logout-button">
              <LogOut size={24} />
            </button>
          </div>
        </div>

        <div className="notifications-container">
          {notifications.length > 0 &&
            notifications.map((notification) => (
              <Notification
                eventId={notification.eventId}
                key={notification.eventId}
                {...notification}
                onClose={(notificationData) =>
                  removeNotification(notification.eventId, notificationData)
                }
                onClick={handleNotificationClick}
              />
            ))}
        </div>

        <div>
          <div className="card full-width">
            <div className="table-container">
              {isDetailView ? (
                <DetailView data={selectedData} onBack={handleBackToList} />
              ) : (
                <>
                  {tableData.length > 0 ? (
                    <table className="events-table">
                      <thead>
                        <tr>
                          {TABLE_HEADERS.map((header) => (
                            <th key={header}>{header}</th>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {tableData.map((entry) => (
                          <tr key={entry.eventId}>
                            <td>{entry.id}</td>
                         
                            <td>{entry.data?.message || "-"}</td>
                            <td>{entry.data?.topic || "-"}</td>
                            <td>
                              <button
                                onClick={() => handleViewData(entry)}
                                className="eye-button"
                              >
                                <Eye size={16} />
                              </button>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  ) : (
                    <div className="empty-table">
                      No events have been received yet
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
