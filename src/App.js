import React, { useEffect } from "react";

function NotificationButton() {
  useEffect(() => {
    // Ask for permission on mount
    if ("Notification" in window && Notification.permission !== "granted") {
      Notification.requestPermission();
    }
  }, []);

  const handleNotification = () => {
    if (!("Notification" in window)) {
      alert("This browser does not support desktop notification");
      return;
    }

    if (Notification.permission === "granted") {
      new Notification("Hello from React!", {
        body: "This is your notification triggered by a button click.",
        icon: "https://via.placeholder.com/128", // optional
      });
    } else if (Notification.permission !== "denied") {
      Notification.requestPermission().then((permission) => {
        if (permission === "granted") {
          new Notification("Thanks!", {
            body: "You just enabled notifications.",
          });
        }
      });
    }
  };

  return (
    <div>
      <button onClick={handleNotification}>
        Trigger Notification
      </button>
    </div>
  );
}

export default NotificationButton;
