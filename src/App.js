import React, { useState, useEffect } from 'react';
import { Bell, BellOff, AlertCircle, Eye, EyeOff } from 'lucide-react';

export default function ChromeNotificationButton() {
  const [permission, setPermission] = useState('default');
  const [isLoading, setIsLoading] = useState(false);
  const [lastNotificationTime, setLastNotificationTime] = useState(null);
  const [debugInfo, setDebugInfo] = useState('');
  const [isTabVisible, setIsTabVisible] = useState(true);

  // Check initial permission status and tab visibility
  useEffect(() => {
    if ('Notification' in window) {
      setPermission(Notification.permission);
      setDebugInfo(`Initial permission: ${Notification.permission}`);
    } else {
      setDebugInfo('Notification API not supported in this browser');
    }

    // Track tab visibility (notifications work better when tab is hidden)
    const handleVisibilityChange = () => {
      setIsTabVisible(!document.hidden);
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, []);

  const requestPermission = async () => {
    if (!('Notification' in window)) {
      setDebugInfo('❌ Notification API not supported');
      return false;
    }

    setIsLoading(true);
    setDebugInfo('🔄 Requesting permission...');
    
    try {
      // For older browsers, use callback version
      let result;
      if (Notification.requestPermission && typeof Notification.requestPermission === 'function') {
        if (Notification.requestPermission.length === 0) {
          // Modern promise-based API
          result = await Notification.requestPermission();
        } else {
          // Legacy callback API
          result = await new Promise((resolve) => {
            Notification.requestPermission(resolve);
          });
        }
      }
      
      setPermission(result);
      setIsLoading(false);
      
      if (result === 'granted') {
        setDebugInfo('✅ Permission granted! You can now receive notifications.');
      } else if (result === 'denied') {
        setDebugInfo('❌ Permission denied. Please enable notifications in Chrome settings.');
      } else {
        setDebugInfo('⚠️ Permission dismissed. Click again to retry.');
      }
      
      return result === 'granted';
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      setDebugInfo(`❌ Error: ${error.message}`);
      setIsLoading(false);
      return false;
    }
  };

  const showChromeNotification = async () => {
    // Check browser support
    if (!('Notification' in window)) {
      setDebugInfo('❌ Your browser doesn\'t support notifications');
      return;
    }

    // Check and request permission
    if (permission !== 'granted') {
      setDebugInfo('🔄 Requesting permission first...');
      const granted = await requestPermission();
      if (!granted) {
        return;
      }
    }

    try {
      setDebugInfo('🚀 Creating Chrome notification...');
      
      // Create notification with basic options (no actions for regular notifications)
      const notification = new Notification('🔔 Chrome Notification Test', {
        body: '👋 Hello! This is a system notification from your React app. Click to focus the tab.',
        icon: 'https://upload.wikimedia.org/wikipedia/commons/thumb/e/e1/Google_Chrome_icon_%28February_2022%29.svg/64px-Google_Chrome_icon_%28February_2022%29.svg.png',
        badge: 'https://upload.wikimedia.org/wikipedia/commons/thumb/e/e1/Google_Chrome_icon_%28February_2022%29.svg/32px-Google_Chrome_icon_%28February_2022%29.svg.png',
        tag: 'chrome-test-notification',
        requireInteraction: false, // Let it auto-dismiss to avoid blocking
        silent: false, // Play notification sound
        timestamp: Date.now()
      });

      // Set up event handlers
      notification.onshow = () => {
        setDebugInfo('✅ Notification displayed successfully!');
        setLastNotificationTime(new Date().toLocaleTimeString());
      };

      notification.onclick = () => {
        setDebugInfo('👆 Notification clicked - focusing tab');
        window.focus();
        notification.close();
      };

      notification.onclose = () => {
        setDebugInfo('📴 Notification closed');
      };

      notification.onerror = (error) => {
        setDebugInfo(`❌ Notification error: ${error}`);
        console.error('Notification error:', error);
      };

      // Auto-close after 6 seconds
      setTimeout(() => {
        if (notification) {
          notification.close();
        }
      }, 6000);

    } catch (error) {
      console.error('Error creating notification:', error);
      setDebugInfo(`❌ Failed to create notification: ${error.message}`);
    }
  };

  const testNotificationSupport = () => {
    const support = {
      notificationAPI: 'Notification' in window,
      permission: permission,
      serviceWorker: 'serviceWorker' in navigator,
      pushManager: 'PushManager' in window
    };
    
    setDebugInfo(`🔍 Browser Support Check:
• Notification API: ${support.notificationAPI ? '✅' : '❌'}
• Current Permission: ${support.permission} ${support.permission === 'granted' ? '✅' : support.permission === 'denied' ? '❌' : '⚠️'}
• Service Worker: ${support.serviceWorker ? '✅' : '❌'}
• Push Manager: ${support.pushManager ? '✅' : '❌'}
• User Agent: ${navigator.userAgent.includes('Chrome') ? 'Chrome ✅' : 'Other Browser ⚠️'}`);
  };

  const getButtonContent = () => {
    if (isLoading) {
      return (
        <>
          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
          <span>Requesting Permission...</span>
        </>
      );
    }

    switch (permission) {
      case 'granted':
        return (
          <>
            <Bell className="h-5 w-5" />
            <span>Send Chrome Notification</span>
          </>
        );
      case 'denied':
        return (
          <>
            <BellOff className="h-5 w-5" />
            <span>Notifications Blocked</span>
          </>
        );
      default:
        return (
          <>
            <AlertCircle className="h-5 w-5" />
            <span>Enable Notifications</span>
          </>
        );
    }
  };

  const getButtonClass = () => {
    const baseClass = "flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 w-full justify-center";
    
    switch (permission) {
      case 'granted':
        return `${baseClass} bg-green-600 hover:bg-green-700 text-white focus:ring-green-500`;
      case 'denied':
        return `${baseClass} bg-red-600 hover:bg-red-700 text-white focus:ring-red-500`;
      default:
        return `${baseClass} bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500`;
    }
  };

  const handleButtonClick = () => {
    if (permission === 'denied') {
      setDebugInfo('❌ Notifications blocked. To enable:\n1. Click the 🔒 or ⓘ icon in address bar\n2. Set Notifications to "Allow"\n3. Refresh this page');
      return;
    }

    if (permission === 'granted') {
      showChromeNotification();
    } else {
      requestPermission();
    }
  };

  return (
    <div className="max-w-lg mx-auto p-6 bg-white rounded-xl shadow-lg">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-2">
          Chrome System Notifications
        </h2>
        <p className="text-gray-600">
          Test notifications that appear outside the browser window
        </p>
      </div>

      <div className="space-y-4">
        {/* Tab Visibility Indicator */}
        <div className="flex items-center justify-center gap-2 text-sm">
          {isTabVisible ? (
            <>
              <Eye className="h-4 w-4 text-blue-500" />
              <span className="text-blue-600">Tab is visible</span>
            </>
          ) : (
            <>
              <EyeOff className="h-4 w-4 text-green-500" />
              <span className="text-green-600">Tab is hidden (better for testing)</span>
            </>
          )}
        </div>

        {/* Main Action Button */}
        <button
          onClick={handleButtonClick}
          disabled={isLoading}
          className={getButtonClass()}
        >
          {getButtonContent()}
        </button>

        {/* Test Support Button */}
        <button
          onClick={testNotificationSupport}
          className="w-full px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg text-sm transition-colors"
        >
          🔍 Test Browser Support
        </button>

        {/* Debug Info */}
        {debugInfo && (
          <div className="bg-gray-900 text-green-400 p-4 rounded-lg text-sm font-mono whitespace-pre-line overflow-x-auto">
            {debugInfo}
          </div>
        )}

        {/* Last Notification Time */}
        {lastNotificationTime && (
          <div className="text-center text-sm text-gray-600 bg-green-50 p-2 rounded">
            Last notification sent: {lastNotificationTime}
          </div>
        )}

        {/* Instructions */}
        <div className="bg-blue-50 p-4 rounded-lg text-sm">
          <h3 className="font-semibold text-blue-800 mb-2">💡 Tips for Testing:</h3>
          <ul className="text-blue-700 space-y-1">
            <li>• <strong>Switch to another tab/app</strong> after clicking the button</li>
            <li>• Look for notifications in your system tray (bottom-right on Windows)</li>
            <li>• On macOS, check the top-right corner of your screen</li>
            <li>• Notifications may have sound alerts</li>
            <li>• Check Chrome's notification settings in chrome://settings/content/notifications</li>
          </ul>
        </div>
      </div>
    </div>
  );
}