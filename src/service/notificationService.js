// Chrome Extension Notification Service
class ChromeNotificationService {
  constructor() {
    this.isExtensionContext = this.checkExtensionContext();
    console.log('ChromeNotificationService initialized, isExtensionContext:', this.isExtensionContext);
  }

  // Check if we're running in Chrome extension context
  checkExtensionContext() {
    try {
      return !!(window.chrome && window.chrome.runtime && window.chrome.runtime.id);
    } catch (error) {
      return false;
    }
  }

  // Send message to Chrome extension background script
  sendMessageToBackground(message) {
    return new Promise((resolve, reject) => {
      // Check if we're in an iframe (React app loaded in extension)
      if (window.parent !== window) {
        // We're in an iframe, send message to parent (content script)
        console.log('Sending message to parent window (content script):', message);
        window.parent.postMessage(message, '*');
        resolve({ success: true, method: 'postMessage' });
        return;
      }

      if (!this.isExtensionContext) {
        console.warn('Not in extension context, cannot send message to background');
        resolve({ success: false, reason: 'Not in extension context' });
        return;
      }

      try {
        window.chrome.runtime.sendMessage(message, (response) => {
          if (window.chrome.runtime.lastError) {
            console.error('Error sending message to background:', window.chrome.runtime.lastError);
            reject(window.chrome.runtime.lastError);
          } else {
            resolve(response);
          }
        });
      } catch (error) {
        console.error('Error in sendMessageToBackground:', error);
        reject(error);
      }
    });
  }

  // Create Chrome native notification
  async createChromeNotification(notificationData) {
    const { message, eventId, title, iconUrl } = notificationData;
    
    const notificationPayload = {
      action: 'create_chrome_notification',
      data: {
        title: title || 'Quickbuzz',
        message: message || 'New notification received',
        iconUrl: iconUrl || null,
        eventId: eventId || `notification_${Date.now()}`
      }
    };

    try {
      const response = await this.sendMessageToBackground(notificationPayload);
      console.log('Chrome notification created:', response);
      return response;
    } catch (error) {
      console.error('Failed to create Chrome notification:', error);
      return { success: false, error };
    }
  }

  // Request notification permission (for web notifications as fallback)
  async requestNotificationPermission() {
    if (!('Notification' in window)) {
      console.warn('This browser does not support notifications');
      return false;
    }

    if (Notification.permission === 'granted') {
      return true;
    }

    if (Notification.permission !== 'denied') {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }

    return false;
  }

  // Create web notification as fallback
  createWebNotification(notificationData) {
    const { title, message, iconUrl } = notificationData;
    
    if (Notification.permission === 'granted') {
      const notification = new Notification(title || 'Quickbuzz', {
        body: message || 'New notification received',
        icon: iconUrl || '/favicon.ico',
        requireInteraction: true,
        tag: 'quickbuzz-notification'
      });

      notification.onclick = () => {
        window.focus();
        notification.close();
      };

      // Auto close after 5 seconds
      setTimeout(() => {
        notification.close();
      }, 5000);

      return notification;
    }
    
    return null;
  }

  // Main method to create notification (tries Chrome first, then web notification)
  async createNotification(notificationData) {
    console.log('Creating notification:', notificationData);
    
    // Try Chrome extension notification first
    if (this.isExtensionContext) {
      const chromeResult = await this.createChromeNotification(notificationData);
      if (chromeResult.success) {
        return { success: true, type: 'chrome', result: chromeResult };
      }
    }

    // Fallback to web notification
    const hasPermission = await this.requestNotificationPermission();
    if (hasPermission) {
      const webNotification = this.createWebNotification(notificationData);
      return { 
        success: !!webNotification, 
        type: 'web', 
        result: webNotification 
      };
    }

    console.warn('No notification method available');
    return { success: false, reason: 'No notification method available' };
  }

  // Open extension (send message to background script)
  async openExtension() {
    try {
      const response = await this.sendMessageToBackground({ action: 'open_extension' });
      console.log('Extension open request sent:', response);
      return response;
    } catch (error) {
      console.error('Failed to open extension:', error);
      return { success: false, error };
    }
  }
}

// Create singleton instance
const chromeNotificationService = new ChromeNotificationService();

export default chromeNotificationService;
