// Comprehensive Notification Service for Chrome Notifications + Toast Integration
class NotificationService {
  constructor() {
    this.isSupported = 'Notification' in window;
    this.permission = this.isSupported ? Notification.permission : 'unsupported';
    this.debugMode = true;
    this.permissionCallbacks = [];
    this.activeNotifications = new Map();
    
    this.log('NotificationService initialized', {
      supported: this.isSupported,
      permission: this.permission
    });
  }

  log(message, data = null) {
    if (this.debugMode) {
    //  console.log(`[NotificationService] ${message}`, data || '');
    }
  }

  // Check if notifications are supported
  isNotificationSupported() {
    return this.isSupported;
  }

  // Get current permission status
  getPermissionStatus() {
    if (!this.isSupported) return 'unsupported';
    return Notification.permission;
  }

  // Add callback for permission changes
  onPermissionChange(callback) {
    this.permissionCallbacks.push(callback);
  }

  // Notify permission change callbacks
  notifyPermissionChange(newPermission) {
    this.permission = newPermission;
    this.permissionCallbacks.forEach(callback => {
      try {
        callback(newPermission);
      } catch (error) {
        this.log('Error in permission callback:', error);
      }
    });
  }

  // Request notification permission
  async requestPermission() {
    if (!this.isSupported) {
      this.log('Notification API not supported');
      return false;
    }

    if (this.permission === 'granted') {
      this.log('Permission already granted');
      return true;
    }

    if (this.permission === 'denied') {
      this.log('Permission denied by user');
      return false;
    }

    try {
      this.log('Requesting notification permission...');
      
      let result;
      if (Notification.requestPermission && typeof Notification.requestPermission === 'function') {
        if (Notification.requestPermission.length === 0) {
          // Modern promise-based API
          result = await Notification.requestPermission();
        } else {
          // Legacy callback API
          result = await new Promise((resolve) => {
            Notification.requestPermission(resolve);
          });
        }
      }
      
      this.log('Permission request result:', result);
      this.notifyPermissionChange(result);
      
      return result === 'granted';
    } catch (error) {
      this.log('Error requesting permission:', error);
      return false;
    }
  }

  // Create Chrome notification
  async createChromeNotification(options = {}) {
    const {
      title = 'Quickbuzz Notification',
      message = 'New notification received',
      icon = 'https://ik.imagekit.io/nz0mhbk9t/shopify-icon.png?updatedAt=1743679874682',
      tag = null,
      requireInteraction = false,
      silent = false,
      autoClose = true,
      autoCloseDelay = 6000,
      onClick = null,
      eventId = null
    } = options;

    // Check support
    if (!this.isSupported) {
      this.log('Cannot create notification: API not supported');
      return { success: false, reason: 'not_supported' };
    }

    // Check permission
    const currentPermission = this.getPermissionStatus();
    if (currentPermission !== 'granted') {
      this.log('Cannot create notification: permission not granted', currentPermission);
      return { success: false, reason: 'permission_denied', permission: currentPermission };
    }

    try {
      this.log('Creating Chrome notification...', { title, message });

      const notificationId = tag || eventId || `notification_${Date.now()}`;
      
      // Create notification
      const notification = new Notification(title, {
        body: message,
        icon: icon,
        tag: notificationId,
        requireInteraction: requireInteraction,
        silent: silent,
        timestamp: Date.now()
      });

      // Store active notification
      this.activeNotifications.set(notificationId, notification);

      // Set up event handlers
      notification.onshow = () => {
        this.log('Notification displayed successfully:', notificationId);
      };

      notification.onclick = () => {
        this.log('Notification clicked:', notificationId);
        window.focus(); // Focus the browser window
        notification.close();
        this.activeNotifications.delete(notificationId);
        
        // Call custom onClick handler if provided
        if (onClick && typeof onClick === 'function') {
          onClick();
        }
      };

      notification.onclose = () => {
        this.log('Notification closed:', notificationId);
        this.activeNotifications.delete(notificationId);
      };

      notification.onerror = (error) => {
        this.log('Notification error:', error);
        this.activeNotifications.delete(notificationId);
      };

      // Auto-close if enabled
      if (autoClose) {
        setTimeout(() => {
          if (this.activeNotifications.has(notificationId)) {
            notification.close();
            this.activeNotifications.delete(notificationId);
          }
        }, autoCloseDelay);
      }

      return { 
        success: true, 
        notification: notification,
        notificationId: notificationId,
        timestamp: new Date().toLocaleTimeString()
      };

    } catch (error) {
      this.log('Error creating notification:', error);
      return { success: false, reason: 'creation_failed', error: error.message };
    }
  }

  // Check if we're in Chrome extension context (iframe)
  isInExtensionContext() {
    return window.parent !== window;
  }

  // Send message to Chrome extension
  sendToExtension(message) {
    if (this.isInExtensionContext()) {
      // We're in an iframe, send to parent (content script)
      window.parent.postMessage(message, '*');
      return true;
    }
    return false;
  }

  // Create Chrome extension notification
  async createExtensionNotification(options = {}) {
    const { title, message, eventId } = options;

    const notificationData = {
      action: 'create_chrome_notification',
      data: {
        title: title || 'Quickbuzz Alert',
        message: message || 'New notification received',
        eventId: eventId || `notification_${Date.now()}`
      }
    };

    if (this.sendToExtension(notificationData)) {
      this.log('Sent notification to Chrome extension:', notificationData);
      return { success: true, type: 'extension' };
    } else {
      this.log('Not in extension context, falling back to web notification');
      return { success: false, reason: 'not_in_extension' };
    }
  }

  // Create notification for Kafka messages (integrates with your existing toast system)
  async createKafkaNotification(kafkaData, showToast = true) {
    const { message, eventId } = kafkaData;

    // Try Chrome extension notification first
    let chromeResult = await this.createExtensionNotification({
      title: 'Quickbuzz Alert',
      message: message || 'New message received',
      eventId: eventId
    });

    // If extension notification failed, try web notification
    if (!chromeResult.success) {
      chromeResult = await this.createChromeNotification({
        title: 'Quickbuzz Alert',
        message: message || 'New message received',
        tag: eventId || `kafka_${Date.now()}`,
        icon: 'https://ik.imagekit.io/nz0mhbk9t/shopify-icon.png?updatedAt=1743679874682',
        requireInteraction: false,
        silent: false,
        autoClose: true,
        autoCloseDelay: 8000,
        eventId: eventId,
        onClick: () => {
          // Focus window when notification is clicked
          window.focus();
        }
      });
    }

    this.log('Kafka notification result:', chromeResult);

    return {
      chrome: chromeResult,
      // Note: Toast notification should be handled by the calling component
      // This service focuses on Chrome notifications
    };
  }

  // Test notification
  async createTestNotification() {
    // Try Chrome extension notification first
    if (this.isInExtensionContext()) {
      const extensionResult = this.sendToExtension({ action: 'test_notification' });
      if (extensionResult) {
        return { success: true, type: 'extension' };
      }
    }

    // Fallback to web notification
    return await this.createChromeNotification({
      title: '🔔 Test Notification',
      message: 'This is a test notification from Quickbuzz! Click to focus the tab.',
      tag: 'test_notification',
      requireInteraction: false,
      autoClose: true,
      autoCloseDelay: 5000
    });
  }

  // Clear all active notifications
  clearAllNotifications() {
    this.activeNotifications.forEach((notification, id) => {
      try {
        notification.close();
      } catch (error) {
        this.log('Error closing notification:', error);
      }
    });
    this.activeNotifications.clear();
    this.log('All notifications cleared');
  }

  // Get browser support info
  getBrowserSupportInfo() {
    return {
      notificationAPI: this.isSupported,
      permission: this.permission,
      serviceWorker: 'serviceWorker' in navigator,
      pushManager: 'PushManager' in window,
      userAgent: navigator.userAgent.includes('Chrome') ? 'Chrome' : 'Other',
      activeNotifications: this.activeNotifications.size
    };
  }
}

// Create singleton instance
const notificationService = new NotificationService();

export default notificationService;
