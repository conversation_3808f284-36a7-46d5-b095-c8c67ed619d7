// Chrome Notification Service
class ChromeNotificationService {
  constructor() {
    this.permission = 'default';
    this.isSupported = 'Notification' in window;
    this.debugMode = true; // Set to false in production
    
    if (this.isSupported) {
      this.permission = Notification.permission;
    }
    
    this.log('ChromeNotificationService initialized', {
      supported: this.isSupported,
      permission: this.permission
    });
  }

  log(message, data = null) {
    if (this.debugMode) {
      console.log(`[ChromeNotification] ${message}`, data || '');
    }
  }

  // Check if notifications are supported
  isNotificationSupported() {
    return this.isSupported;
  }

  // Get current permission status
  getPermissionStatus() {
    if (!this.isSupported) return 'unsupported';
    return Notification.permission;
  }

  // Request notification permission
  async requestPermission() {
    if (!this.isSupported) {
      this.log('Notification API not supported');
      return false;
    }

    if (this.permission === 'granted') {
      this.log('Permission already granted');
      return true;
    }

    if (this.permission === 'denied') {
      this.log('Permission denied by user');
      return false;
    }

    try {
      this.log('Requesting notification permission...');
      
      let result;
      if (Notification.requestPermission && typeof Notification.requestPermission === 'function') {
        if (Notification.requestPermission.length === 0) {
          // Modern promise-based API
          result = await Notification.requestPermission();
        } else {
          // Legacy callback API
          result = await new Promise((resolve) => {
            Notification.requestPermission(resolve);
          });
        }
      }
      
      this.permission = result;
      this.log('Permission request result:', result);
      
      return result === 'granted';
    } catch (error) {
      this.log('Error requesting permission:', error);
      return false;
    }
  }

  // Create Chrome notification
  async createNotification(options = {}) {
    const {
      title = 'Quickbuzz Notification',
      message = 'New notification received',
      icon = null,
      tag = null,
      requireInteraction = false,
      silent = false,
      autoClose = true,
      autoCloseDelay = 6000,
      onClick = null
    } = options;

    // Check support
    if (!this.isSupported) {
      this.log('Cannot create notification: API not supported');
      return { success: false, reason: 'not_supported' };
    }

    // Check permission
    if (this.permission !== 'granted') {
      this.log('Cannot create notification: permission not granted');
      return { success: false, reason: 'permission_denied' };
    }

    try {
      this.log('Creating Chrome notification...', { title, message });

      // Create notification
      const notification = new Notification(title, {
        body: message,
        icon: icon || 'https://ik.imagekit.io/nz0mhbk9t/shopify-icon.png?updatedAt=1743679874682',
        badge: 'https://ik.imagekit.io/nz0mhbk9t/shopify-icon.png?updatedAt=1743679874682',
        tag: tag || `quickbuzz_${Date.now()}`,
        requireInteraction: requireInteraction,
        silent: silent,
        timestamp: Date.now()
      });

      // Set up event handlers
      notification.onshow = () => {
        this.log('Notification displayed successfully');
      };

      notification.onclick = () => {
        this.log('Notification clicked');
        window.focus(); // Focus the browser window
        notification.close();
        
        // Call custom onClick handler if provided
        if (onClick && typeof onClick === 'function') {
          onClick();
        }
      };

      notification.onclose = () => {
        this.log('Notification closed');
      };

      notification.onerror = (error) => {
        this.log('Notification error:', error);
      };

      // Auto-close if enabled
      if (autoClose) {
        setTimeout(() => {
          if (notification) {
            notification.close();
          }
        }, autoCloseDelay);
      }

      return { 
        success: true, 
        notification: notification,
        timestamp: new Date().toLocaleTimeString()
      };

    } catch (error) {
      this.log('Error creating notification:', error);
      return { success: false, reason: 'creation_failed', error: error.message };
    }
  }

  // Create notification for Kafka messages (your specific use case)
  async createKafkaNotification(kafkaData) {
    const { message, eventId } = kafkaData;
    
    return await this.createNotification({
      title: 'Quickbuzz Alert',
      message: message || 'New message received',
      tag: eventId || `kafka_${Date.now()}`,
      icon: 'https://ik.imagekit.io/nz0mhbk9t/shopify-icon.png?updatedAt=1743679874682',
      requireInteraction: false,
      silent: false,
      autoClose: true,
      autoCloseDelay: 8000,
      onClick: () => {
        // Focus window when notification is clicked
        window.focus();
        // You can add more custom logic here if needed
      }
    });
  }

  // Test notification
  async createTestNotification() {
    return await this.createNotification({
      title: '🔔 Test Notification',
      message: 'This is a test notification from Quickbuzz! Click to focus the tab.',
      tag: 'test_notification',
      requireInteraction: false,
      autoClose: true,
      autoCloseDelay: 5000
    });
  }

  // Get browser support info
  getBrowserSupportInfo() {
    return {
      notificationAPI: this.isSupported,
      permission: this.permission,
      serviceWorker: 'serviceWorker' in navigator,
      pushManager: 'PushManager' in window,
      userAgent: navigator.userAgent.includes('Chrome') ? 'Chrome' : 'Other'
    };
  }
}

// Create singleton instance
const chromeNotificationService = new ChromeNotificationService();

export default chromeNotificationService;
