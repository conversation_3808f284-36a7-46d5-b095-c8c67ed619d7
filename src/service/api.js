import axios from "axios";
import { API_BASE_URL, JWT_SECRET } from "../config/const";

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
    Authorization: `Bearer ${JWT_SECRET}`,
  },
});

export const authService = {
  login: async (credentials) => {
    try {
      const response = await api.post("/auth", credentials);
      return response;
    } catch (error) {
      throw error;
    }
  },
};

export const eventService = {
  updateViewCount: async (data) => {
    try {
      const response = await api.post("/update-view-count", data);
      return response;
    } catch (error) {
      throw error;
    }
  },
  getEvents: async (data) => {
    console.log("data", data);
    try {
      const response = await api.post("/get-events", data);
      return response;
    } catch (error) {
      throw error;
    }
  },
};
